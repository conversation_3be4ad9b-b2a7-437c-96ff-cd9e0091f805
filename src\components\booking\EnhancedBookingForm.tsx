
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { CalendarIcon, Plus, Minus, User, CreditCard, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';

interface BookingData {
  tourId: string;
  startDate: Date | null;
  groupSize: number;
  travelers: Array<{
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: Date | null;
    passportNumber: string;
    nationality: string;
  }>;
  accommodation: string;
  addOns: string[];
  specialRequests: string;
  totalPrice: number;
}

interface EnhancedBookingFormProps {
  tourId: string;
  currentStep: number;
  bookingData: BookingData;
  onDataChange: (field: keyof BookingData, value: any) => void;
  onNext: () => void;
  onPrev: () => void;
  customTour?: any;
}

const EnhancedBookingForm: React.FC<EnhancedBookingFormProps> = ({
  tourId,
  currentStep,
  bookingData,
  onDataChange,
  onNext,
  onPrev,
  customTour
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const accommodationOptions = [
    { value: 'budget', label: 'Budget Camping', price: 0 },
    { value: 'midrange', label: 'Mid-range Lodge', price: 150 },
    { value: 'luxury', label: 'Luxury Safari Lodge', price: 400 },
  ];

  const addOnOptions = [
    { value: 'photography', label: 'Photography Guide', price: 75 },
    { value: 'cultural', label: 'Cultural Village Visit', price: 50 },
    { value: 'balloon', label: 'Hot Air Balloon', price: 550 },
    { value: 'night-drive', label: 'Night Game Drive', price: 100 },
  ];

  const updateTravelerInfo = (index: number, field: string, value: any) => {
    const updatedTravelers = [...bookingData.travelers];
    updatedTravelers[index] = { ...updatedTravelers[index], [field]: value };
    onDataChange('travelers', updatedTravelers);
  };

  const addTraveler = () => {
    const newTraveler = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: null,
      passportNumber: '',
      nationality: '',
    };
    onDataChange('travelers', [...bookingData.travelers, newTraveler]);
  };

  const removeTraveler = (index: number) => {
    const updatedTravelers = bookingData.travelers.filter((_, i) => i !== index);
    onDataChange('travelers', updatedTravelers);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    // Simulate booking submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsSubmitting(false);
    onNext();
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CalendarIcon className="mr-2 h-5 w-5" />
                Select Dates & Group Size
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {bookingData.startDate ? format(bookingData.startDate, 'PPP') : 'Pick a date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={bookingData.startDate || undefined}
                      onSelect={(date) => onDataChange('startDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label htmlFor="groupSize">Group Size</Label>
                <div className="flex items-center space-x-4 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onDataChange('groupSize', Math.max(1, bookingData.groupSize - 1))}
                    disabled={bookingData.groupSize <= 1}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="text-xl font-semibold w-12 text-center">{bookingData.groupSize}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onDataChange('groupSize', Math.min(12, bookingData.groupSize + 1))}
                    disabled={bookingData.groupSize >= 12}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {customTour && (
                <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                  <h4 className="font-semibold text-orange-800 mb-2">Custom Tour Details</h4>
                  <p className="text-orange-700">Duration: {customTour.duration}</p>
                  <p className="text-orange-700">Price: ${customTour.price}</p>
                </div>
              )}
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Traveler Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              {Array.from({ length: bookingData.groupSize }).map((_, index) => (
                <div key={index} className="border rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-semibold">Traveler {index + 1}</h4>
                    {index > 0 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeTraveler(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>First Name</Label>
                      <Input
                        value={bookingData.travelers[index]?.firstName || ''}
                        onChange={(e) => updateTravelerInfo(index, 'firstName', e.target.value)}
                        placeholder="Enter first name"
                      />
                    </div>
                    <div>
                      <Label>Last Name</Label>
                      <Input
                        value={bookingData.travelers[index]?.lastName || ''}
                        onChange={(e) => updateTravelerInfo(index, 'lastName', e.target.value)}
                        placeholder="Enter last name"
                      />
                    </div>
                    <div>
                      <Label>Email</Label>
                      <Input
                        type="email"
                        value={bookingData.travelers[index]?.email || ''}
                        onChange={(e) => updateTravelerInfo(index, 'email', e.target.value)}
                        placeholder="Enter email"
                      />
                    </div>
                    <div>
                      <Label>Phone</Label>
                      <Input
                        value={bookingData.travelers[index]?.phone || ''}
                        onChange={(e) => updateTravelerInfo(index, 'phone', e.target.value)}
                        placeholder="Enter phone number"
                      />
                    </div>
                    <div>
                      <Label>Passport Number</Label>
                      <Input
                        value={bookingData.travelers[index]?.passportNumber || ''}
                        onChange={(e) => updateTravelerInfo(index, 'passportNumber', e.target.value)}
                        placeholder="Enter passport number"
                      />
                    </div>
                    <div>
                      <Label>Nationality</Label>
                      <Input
                        value={bookingData.travelers[index]?.nationality || ''}
                        onChange={(e) => updateTravelerInfo(index, 'nationality', e.target.value)}
                        placeholder="Enter nationality"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        );

      case 3:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Customize Your Experience</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label>Accommodation</Label>
                <Select value={bookingData.accommodation} onValueChange={(value) => onDataChange('accommodation', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select accommodation" />
                  </SelectTrigger>
                  <SelectContent>
                    {accommodationOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label} {option.price > 0 && `(+$${option.price}/night)`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Add-ons</Label>
                <div className="space-y-3 mt-2">
                  {addOnOptions.map((addon) => (
                    <div key={addon.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={addon.value}
                        checked={bookingData.addOns.includes(addon.value)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            onDataChange('addOns', [...bookingData.addOns, addon.value]);
                          } else {
                            onDataChange('addOns', bookingData.addOns.filter(a => a !== addon.value));
                          }
                        }}
                      />
                      <Label htmlFor={addon.value} className="flex-1">
                        {addon.label} (+${addon.price})
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label htmlFor="specialRequests">Special Requests</Label>
                <Textarea
                  id="specialRequests"
                  value={bookingData.specialRequests}
                  onChange={(e) => onDataChange('specialRequests', e.target.value)}
                  placeholder="Any special dietary requirements, accessibility needs, or other requests..."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        );

      case 4:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Payment & Confirmation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <div className="flex items-center text-green-800">
                  <CheckCircle className="mr-2 h-5 w-5" />
                  <span className="font-semibold">Booking Summary</span>
                </div>
                <div className="mt-2 text-green-700">
                  <p>Tour: {customTour ? customTour.title || 'Custom Safari' : 'Safari Adventure'}</p>
                  <p>Date: {bookingData.startDate ? format(bookingData.startDate, 'PPP') : 'Not selected'}</p>
                  <p>Group Size: {bookingData.groupSize} travelers</p>
                  <p>Total Price: ${bookingData.totalPrice}</p>
                </div>
              </div>

              <div className="text-center">
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                >
                  {isSubmitting ? 'Processing...' : 'Complete Booking'}
                </Button>
              </div>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {renderStep()}
      
      <div className="flex justify-between">
        <Button
          onClick={onPrev}
          disabled={currentStep === 1}
          variant="outline"
        >
          Previous
        </Button>
        
        {currentStep < 4 && (
          <Button
            onClick={onNext}
            className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
          >
            Next
          </Button>
        )}
      </div>
    </div>
  );
};

export default EnhancedBookingForm;
