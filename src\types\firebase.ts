import { Timestamp } from 'firebase/firestore';

// User Types
export interface UserProfile {
  uid: string;
  email: string;
  displayName: string;
  role: 'user' | 'admin' | 'guide';
  phone?: string;
  country?: string;
  profileImage?: string;
  dateOfBirth?: Date;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  preferences?: {
    accommodation: 'budget' | 'midrange' | 'luxury';
    activities: string[];
    dietaryRestrictions: string[];
    fitnessLevel: 'low' | 'moderate' | 'high' | 'very-high';
    photographyInterest: boolean;
    birdingInterest: boolean;
  };
  loyaltyPoints?: number;
  pastBookings?: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Enhanced Tour Types
export interface Tour {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  location: string;
  destinations: string[];
  activities: string[];
  accommodations: string[];
  maxGroupSize: number;
  minGroupSize: number;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'extreme';
  includes: string[];
  excludes: string[];
  images: string[];
  featured: boolean;
  status: 'active' | 'inactive' | 'draft';
  rating: number;
  reviewCount: number;
  
  // New Advanced Features
  tourType: 'standard' | 'photography' | 'birding' | 'walking' | 'night-drive' | 'conservation' | 'cultural';
  seasonality: {
    greenSeason: boolean;
    drySeason: boolean;
    bestMonths: string[];
  };
  itinerary: DailyItinerary[];
  fitnessRequirements: FitnessRequirement;
  equipment: Equipment;
  groupOptions: GroupOption[];
  specialFeatures: string[];
  difficultyDetails: string;
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface DailyItinerary {
  day: number;
  title: string;
  description: string;
  accommodation: string;
  meals: string[];
  activities: DailyActivity[];
  drivingTime: string;
  highlights: string[];
}

export interface DailyActivity {
  time: string;
  activity: string;
  description: string;
  duration: string;
  location: string;
}

export interface FitnessRequirement {
  level: 'low' | 'moderate' | 'high' | 'very-high';
  description: string;
  walkingDistance: string;
  terrain: string;
  ageRestrictions?: string;
  medicalConditions?: string[];
}

export interface Equipment {
  provided: EquipmentItem[];
  recommended: EquipmentItem[];
  required: EquipmentItem[];
}

export interface EquipmentItem {
  name: string;
  description: string;
  category: 'clothing' | 'gear' | 'photography' | 'birding' | 'safety';
  optional: boolean;
}

export interface GroupOption {
  type: 'private' | 'shared' | 'guaranteed';
  minParticipants: number;
  maxParticipants: number;
  pricePerPerson: number;
  description: string;
}

// Enhanced Booking Types
export interface Booking {
  id: string;
  userId: string;
  tourId: string;
  tourTitle: string;
  userName: string;
  userEmail: string;
  startDate: string;
  endDate: string;
  guests: number;
  travelers: Traveler[];
  accommodation: string;
  addOns: string[];
  specialRequests: string;
  totalPrice: number;
  depositPaid: number;
  balanceRemaining: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  paymentStatus: 'pending' | 'partial' | 'paid' | 'refunded';
  guideId?: string;
  
  // New Enhanced Features
  groupOption: 'private' | 'shared' | 'guaranteed';
  packageBookings?: string[]; // For multi-tour packages
  insurancePolicy?: InsurancePolicy;
  visaAssistance?: VisaAssistance;
  airportTransfer?: AirportTransfer;
  dietaryRequirements: string[];
  medicalConditions: string[];
  fitnessLevel: string;
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface InsurancePolicy {
  provider: string;
  policyNumber: string;
  coverage: string;
  premium: number;
  purchased: boolean;
}

export interface VisaAssistance {
  required: boolean;
  country: string;
  processingTime: string;
  fee: number;
  documents: string[];
  status: 'not-started' | 'in-progress' | 'approved' | 'rejected';
}

export interface AirportTransfer {
  pickup: {
    location: string;
    time: string;
    flightNumber?: string;
  };
  dropoff: {
    location: string;
    time: string;
    flightNumber?: string;
  };
  vehicle: string;
  cost: number;
}

export interface Traveler {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: Date;
  passportNumber: string;
  nationality: string;
  dietaryRestrictions?: string[];
  medicalConditions?: string[];
  fitnessLevel: 'low' | 'moderate' | 'high' | 'very-high';
  photographyExperience?: 'beginner' | 'intermediate' | 'advanced';
  birdingExperience?: 'beginner' | 'intermediate' | 'advanced';
}

// Enhanced Destination Types
export interface Destination {
  id: string;
  name: string;
  description: string;
  country: string;
  region: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  bestTimeToVisit: string[];
  climate: string;
  wildlife: WildlifeInfo[];
  images: string[];
  activities: string[];
  accommodations: string[];
  featured: boolean;
  
  // New Enhanced Features
  detailedGuide: DestinationGuide;
  seasonalInfo: SeasonalInfo;
  conservationInfo: ConservationInfo;
  culturalInfo: CulturalInfo;
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface DestinationGuide {
  overview: string;
  geography: string;
  history: string;
  bestTimeToVisit: {
    drySeason: string;
    greenSeason: string;
    photography: string;
    birding: string;
  };
  gettingThere: string;
  accommodation: string;
  packingTips: string[];
  healthSafety: string;
  travelTips: string[];
}

export interface SeasonalInfo {
  drySeason: {
    months: string[];
    description: string;
    wildlife: string;
    photography: string;
    advantages: string[];
    disadvantages: string[];
  };
  greenSeason: {
    months: string[];
    description: string;
    wildlife: string;
    photography: string;
    advantages: string[];
    disadvantages: string[];
  };
}

export interface WildlifeInfo {
  species: string;
  scientificName: string;
  category: 'big-five' | 'predator' | 'herbivore' | 'bird' | 'primate' | 'reptile';
  abundance: 'common' | 'moderate' | 'rare' | 'seasonal';
  bestSpottingTime: string;
  behavior: string;
  conservationStatus: string;
  photographyTips?: string;
}

export interface ConservationInfo {
  initiatives: string[];
  challenges: string[];
  howTouristsHelp: string[];
  conservationFee: number;
}

export interface CulturalInfo {
  tribes: string[];
  languages: string[];
  traditions: string[];
  etiquette: string[];
  culturalSites: string[];
}

// New Package System
export interface TourPackage {
  id: string;
  title: string;
  description: string;
  tourIds: string[];
  totalDuration: string;
  totalPrice: number;
  discount: number;
  includes: string[];
  itinerary: PackageItinerary[];
  featured: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface PackageItinerary {
  phase: string;
  description: string;
  duration: string;
  tourId: string;
  accommodation: string;
}

// Guide System
export interface Guide {
  id: string;
  name: string;
  email: string;
  phone: string;
  languages: string[];
  specializations: string[];
  experience: number;
  rating: number;
  bio: string;
  image: string;
  certifications: string[];
  availability: GuideAvailability[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface GuideAvailability {
  startDate: string;
  endDate: string;
  available: boolean;
  tourId?: string;
}

// Content Management
export interface TravelGuide {
  id: string;
  title: string;
  category: 'health' | 'packing' | 'cultural' | 'wildlife' | 'photography' | 'general';
  content: string;
  tags: string[];
  featured: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface PackingList {
  id: string;
  tourType: string;
  season: 'dry' | 'green' | 'year-round';
  items: PackingItem[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface PackingItem {
  name: string;
  category: 'clothing' | 'gear' | 'personal' | 'documents' | 'medical';
  essential: boolean;
  description: string;
  alternatives?: string[];
}

// Keep existing types but add enhanced versions
export interface Review {
  id: string;
  userId: string;
  tourId: string;
  bookingId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  comment: string;
  images?: string[];
  verified: boolean;
  helpful: number;
  
  // Enhanced review features
  categories: ReviewCategory[];
  wouldRecommend: boolean;
  bestAspects: string[];
  improvements: string[];
  wildlifeSightings: string[];
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ReviewCategory {
  category: 'guide' | 'accommodation' | 'food' | 'wildlife' | 'value' | 'organization';
  rating: number;
}

// Activity Types
export interface Activity {
  id: string;
  name: string;
  description: string;
  category: 'wildlife' | 'adventure' | 'cultural' | 'photography' | 'relaxation' | 'conservation' | 'birding';
  difficulty: 'easy' | 'moderate' | 'challenging' | 'extreme';
  duration: string;
  minAge: number;
  maxGroupSize: number;
  equipment: string[];
  price: number;
  destinations: string[];
  images: string[];
  seasonal: boolean;
  
  // Enhanced activity features
  fitnessRequired: 'low' | 'moderate' | 'high' | 'very-high';
  specialRequirements: string[];
  wildlifeSpotting: WildlifeSpotting[];
  photographyOpportunities: string[];
  conservationAspect?: string;
  culturalSignificance?: string;
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface WildlifeSpotting {
  species: string;
  probability: 'guaranteed' | 'very-likely' | 'likely' | 'possible' | 'rare';
  season: 'year-round' | 'dry-season' | 'green-season';
  bestTime: string;
}

// Accommodation Types
export interface Accommodation {
  id: string;
  name: string;
  description: string;
  type: 'lodge' | 'camp' | 'hotel' | 'resort' | 'guesthouse' | 'mobile-camp';
  category: 'budget' | 'midrange' | 'luxury' | 'ultra-luxury';
  location: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  amenities: string[];
  roomTypes: RoomType[];
  images: string[];
  rating: number;
  reviewCount: number;
  pricePerNight: number;
  maxGuests: number;
  
  // Enhanced accommodation features
  sustainability: SustainabilityInfo;
  wildlifeViewing: boolean;
  photographyFacilities: string[];
  conservationPrograms: string[];
  culturalExperiences: string[];
  accessibility: string[];
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface SustainabilityInfo {
  ecoFriendly: boolean;
  localCommunitySupport: boolean;
  conservationEfforts: string[];
  sustainablePractices: string[];
  certifications: string[];
}

export interface RoomType {
  name: string;
  description: string;
  maxOccupancy: number;
  price: number;
  amenities: string[];
  images: string[];
  wildlifeViewing: boolean;
}

// Keep all existing types...
export interface Wishlist {
  id: string;
  userId: string;
  tourIds: string[];
  destinationIds: string[];
  packageIds: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface WildlifeSighting {
  id: string;
  userId: string;
  userName: string;
  tourId?: string;
  destinationId: string;
  animalName: string;
  animalSpecies: string;
  count: number;
  behavior: string;
  location: {
    lat: number;
    lng: number;
    name: string;
  };
  date: Timestamp;
  time: string;
  weather: string;
  images: string[];
  verified: boolean;
  guide?: string;
  photographyDetails?: PhotographyDetails;
  createdAt: Timestamp;
}

export interface PhotographyDetails {
  camera: string;
  lens: string;
  settings: string;
  tips: string;
}

export interface ChatMessage {
  id: string;
  sessionId: string;
  userId?: string;
  userName?: string;
  message: string;
  isBot: boolean;
  timestamp: Timestamp;
  category?: 'booking' | 'tour-info' | 'general' | 'support' | 'emergency';
  expertType?: 'safari' | 'photography' | 'birding' | 'cultural' | 'general';
}

export interface ChatSession {
  id: string;
  userId?: string;
  startTime: Timestamp;
  endTime?: Timestamp;
  resolved: boolean;
  assignedAgent?: string;
  category: string;
  summary?: string;
  expertType?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface WeatherData {
  id: string;
  destinationId: string;
  date: string;
  temperature: {
    high: number;
    low: number;
  };
  conditions: string;
  humidity: number;
  windSpeed: number;
  rainfall: number;
  visibility: number;
  uvIndex: number;
  
  // Enhanced weather features
  wildlifeActivity: string;
  photographyConditions: string;
  roadConditions: string;
  recommendations: string[];
  
  createdAt: Timestamp;
}

export interface VirtualTour {
  id: string;
  tourId: string;
  title: string;
  description: string;
  scenes: VirtualTourScene[];
  duration: number;
  featured: boolean;
  viewCount: number;
  
  // Enhanced virtual tour features
  wildlifeSpottingPoints: WildlifeSpottingPoint[];
  photographyTips: PhotographyTip[];
  conservationInfo: string[];
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface VirtualTourScene {
  id: string;
  title: string;
  description: string;
  panoramaUrl: string;
  hotspots: Hotspot[];
  audio?: string;
  order: number;
  timeOfDay: 'dawn' | 'morning' | 'midday' | 'afternoon' | 'sunset' | 'night';
  season: 'dry' | 'green';
}

export interface WildlifeSpottingPoint {
  sceneId: string;
  species: string;
  position: { x: number; y: number };
  probability: string;
  description: string;
}

export interface PhotographyTip {
  sceneId: string;
  position: { x: number; y: number };
  tip: string;
  settings: string;
  bestTime: string;
}

export interface Hotspot {
  id: string;
  type: 'info' | 'navigation' | 'media' | 'wildlife' | 'photography';
  position: {
    x: number;
    y: number;
  };
  content: string;
  targetScene?: string;
  mediaUrl?: string;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'booking' | 'payment' | 'tour-update' | 'weather-alert' | 'wildlife-sighting' | 'general' | 'conservation' | 'photography';
  read: boolean;
  actionUrl?: string;
  data?: any;
  priority: 'low' | 'medium' | 'high';
  createdAt: Timestamp;
}

export interface PaymentTransaction {
  id: string;
  bookingId: string;
  userId: string;
  amount: number;
  currency: string;
  type: 'deposit' | 'balance' | 'refund' | 'fee' | 'insurance' | 'visa' | 'transfer';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  paymentMethod: string;
  transactionId: string;
  gatewayResponse?: any;
  description: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ContentPage {
  id: string;
  slug: string;
  title: string;
  content: string;
  metaDescription: string;
  keywords: string[];
  published: boolean;
  authorId: string;
  category: 'guide' | 'destination' | 'wildlife' | 'photography' | 'conservation' | 'cultural';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string;
  authorId: string;
  authorName: string;
  category: string;
  tags: string[];
  published: boolean;
  publishedAt?: Timestamp;
  viewCount: number;
  
  // Enhanced blog features
  wildlifeSpotted: string[];
  photographyTips: string[];
  conservationMessage?: string;
  culturalInsights: string[];
  seasonalRelevance: string[];
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// New Search and Filter Types
export interface SearchFilters {
  destination?: string[];
  duration?: {
    min: number;
    max: number;
  };
  price?: {
    min: number;
    max: number;
  };
  difficulty?: string[];
  tourType?: string[];
  season?: string[];
  wildlife?: string[];
  activities?: string[];
  groupSize?: number;
  fitnessLevel?: string[];
  photography?: boolean;
  birding?: boolean;
  conservation?: boolean;
  cultural?: boolean;
}

export interface TourAvailability {
  tourId: string;
  date: string;
  available: boolean;
  spotsRemaining: number;
  priceModifier: number;
  season: 'high' | 'medium' | 'low';
  wildlifeActivity: 'excellent' | 'good' | 'fair' | 'poor';
  weatherConditions: 'excellent' | 'good' | 'fair' | 'poor';
}
