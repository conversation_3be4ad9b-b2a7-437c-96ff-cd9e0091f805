
import React, { useState, useEffect } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import TourCard from '@/components/tours/TourCard';
import TourFilters from '@/components/tours/TourFilters';
import { Button } from '@/components/ui/button';
import { Grid, List, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface Tour {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  image: string;
  category: string;
  accommodationLevel: string;
  destinations: string[];
  rating: number;
  reviews: number;
}

const Tours = () => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [filteredTours, setFilteredTours] = useState<Tour[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    category: 'all',
    accommodationLevel: 'all',
    priceRange: [0, 10000],
    duration: 'all',
    destinations: []
  });

  // Sample tour data
  useEffect(() => {
    const sampleTours: Tour[] = [
      {
        id: '1',
        title: 'Serengeti Classic Safari',
        description: 'Experience the Great Migration in the world-famous Serengeti National Park',
        price: 2499,
        duration: '5 days, 4 nights',
        image: 'photo-1472396961693-142e6e269027',
        category: 'Wildlife Safari',
        accommodationLevel: 'Luxury',
        destinations: ['Serengeti', 'Ngorongoro'],
        rating: 4.8,
        reviews: 124
      },
      {
        id: '2',
        title: 'Budget Northern Circuit',
        description: 'Affordable safari covering the best of Northern Tanzania',
        price: 899,
        duration: '4 days, 3 nights',
        image: 'photo-1466721591366-2d5fba72006d',
        category: 'Wildlife Safari',
        accommodationLevel: 'Budget',
        destinations: ['Tarangire', 'Lake Manyara', 'Ngorongoro'],
        rating: 4.5,
        reviews: 89
      },
      {
        id: '3',
        title: 'Cultural Heritage Tour',
        description: 'Immerse yourself in Maasai culture and traditions',
        price: 599,
        duration: '3 days, 2 nights',
        image: 'photo-1493962853295-0fd70327578a',
        category: 'Cultural',
        accommodationLevel: 'Mid-range',
        destinations: ['Maasai Villages', 'Olduvai Gorge'],
        rating: 4.6,
        reviews: 67
      },
      {
        id: '4',
        title: 'Kilimanjaro Trekking Adventure',
        description: 'Conquer Africa\'s highest peak via the Machame route',
        price: 1899,
        duration: '7 days, 6 nights',
        image: 'photo-1485833077593-4278bba3f11f',
        category: 'Adventure',
        accommodationLevel: 'Mid-range',
        destinations: ['Mount Kilimanjaro'],
        rating: 4.9,
        reviews: 156
      }
    ];
    setTours(sampleTours);
    setFilteredTours(sampleTours);
  }, []);

  // Filter tours based on search and filters
  useEffect(() => {
    let filtered = tours.filter(tour => {
      const matchesSearch = tour.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           tour.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = filters.category === 'all' || tour.category === filters.category;
      const matchesAccommodation = filters.accommodationLevel === 'all' || tour.accommodationLevel === filters.accommodationLevel;
      const matchesPrice = tour.price >= filters.priceRange[0] && tour.price <= filters.priceRange[1];
      
      return matchesSearch && matchesCategory && matchesAccommodation && matchesPrice;
    });
    
    setFilteredTours(filtered);
  }, [tours, searchTerm, filters]);

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Page Header */}
        <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-12 md:py-16">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4">Safari Tours</h1>
            <p className="text-lg md:text-xl max-w-2xl">
              Discover our carefully curated collection of safari experiences in Tanzania
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-6 md:py-8">
          <div className="flex flex-col lg:flex-row gap-6 md:gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/4">
              <div className="lg:sticky lg:top-24">
                <TourFilters 
                  filters={filters} 
                  onFiltersChange={setFilters}
                />
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:w-3/4">
              {/* Search and View Controls */}
              <div className="flex flex-col sm:flex-row gap-3 md:gap-4 mb-4 md:mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search tours..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 text-sm md:text-base"
                  />
                </div>
                <div className="flex gap-2 self-start sm:self-auto">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="icon"
                    onClick={() => setViewMode('grid')}
                    className="h-9 w-9 md:h-10 md:w-10"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="icon"
                    onClick={() => setViewMode('list')}
                    className="h-9 w-9 md:h-10 md:w-10"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Results Summary */}
              <div className="mb-4 md:mb-6">
                <p className="text-gray-600 text-sm md:text-base">
                  Showing {filteredTours.length} of {tours.length} tours
                </p>
              </div>

              {/* Tours Grid/List */}
              <div className={viewMode === 'grid' 
                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6' 
                : 'space-y-4 md:space-y-6'}>
                {filteredTours.map((tour) => (
                  <TourCard 
                    key={tour.id} 
                    tour={tour} 
                    viewMode={viewMode}
                  />
                ))}
              </div>

              {filteredTours.length === 0 && (
                <div className="text-center py-8 md:py-12">
                  <p className="text-gray-500 text-base md:text-lg mb-4">No tours found matching your criteria.</p>
                  <Button 
                    onClick={() => {
                      setSearchTerm('');
                      setFilters({
                        category: 'all',
                        accommodationLevel: 'all',
                        priceRange: [0, 10000],
                        duration: 'all',
                        destinations: []
                      });
                    }}
                    className="text-sm md:text-base px-4 md:px-6"
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Tours;
