
import React from 'react';
import { Star, Quote } from 'lucide-react';

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      location: 'New York, USA',
      rating: 5,
      text: 'Absolutely incredible experience! The guides were knowledgeable, the accommodations were luxurious, and seeing the Great Migration was a dream come true.',
      tour: 'Serengeti Great Migration Safari',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?auto=format&fit=crop&w=150&h=150'
    },
    {
      name: '<PERSON>',
      location: 'London, UK',
      rating: 5,
      text: 'The attention to detail was phenomenal. Every day brought new adventures and the wildlife viewing exceeded all expectations. Highly recommend!',
      tour: 'Ngorongoro Crater Adventure',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&h=150'
    },
    {
      name: '<PERSON>',
      location: 'Madrid, Spain',
      rating: 5,
      text: 'The perfect blend of adventure and comfort. The cultural experiences with local communities were as memorable as the wildlife encounters.',
      tour: 'Cultural & Wildlife Safari',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=150&h=150'
    }
  ];

  return (
    <section className="py-16 bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            What Our Travelers Say
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Don't just take our word for it. Here's what our guests have to say 
            about their unforgettable safari experiences.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-gray-800 rounded-xl p-6 hover:bg-gray-750 transition-colors"
            >
              {/* Quote Icon */}
              <Quote className="h-8 w-8 text-orange-500 mb-4" />

              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Testimonial Text */}
              <p className="text-gray-300 mb-6 leading-relaxed">
                "{testimonial.text}"
              </p>

              {/* Tour Name */}
              <p className="text-orange-500 text-sm font-medium mb-4">
                {testimonial.tour}
              </p>

              {/* Author Info */}
              <div className="flex items-center">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="h-12 w-12 rounded-full object-cover mr-4"
                />
                <div>
                  <h4 className="font-semibold">{testimonial.name}</h4>
                  <p className="text-gray-400 text-sm">{testimonial.location}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-8 border-t border-gray-800">
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-500 mb-2">4.9/5</div>
            <div className="text-gray-400">Average Rating</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-500 mb-2">98%</div>
            <div className="text-gray-400">Would Recommend</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-500 mb-2">1,200+</div>
            <div className="text-gray-400">Reviews</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-500 mb-2">15+</div>
            <div className="text-gray-400">Awards Won</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
