
import { FirebaseService } from './firebase';
import { Tour, SearchFilters } from '@/types/firebase';

export class TourService {
  // Get all tours with optional filters
  static async getTours(filters?: SearchFilters) {
    try {
      if (filters) {
        return await FirebaseService.getToursWithFilters(filters);
      }
      return await FirebaseService.getTours();
    } catch (error) {
      console.error('Error getting tours:', error);
      throw error;
    }
  }

  // Get featured tours
  static async getFeaturedTours(limit: number = 6) {
    try {
      return await FirebaseService.getFeaturedTours(limit);
    } catch (error) {
      console.error('Error getting featured tours:', error);
      throw error;
    }
  }

  // Search tours
  static async searchTours(searchTerm: string, filters?: SearchFilters) {
    try {
      return await FirebaseService.searchTours(searchTerm, filters);
    } catch (error) {
      console.error('Error searching tours:', error);
      throw error;
    }
  }

  // Get tour details
  static async getTourDetails(tourId: string) {
    try {
      const tour = await FirebaseService.getTour(tourId);
      if (!tour) {
        throw new Error('Tour not found');
      }

      // Get related data
      const [reviews, availability] = await Promise.all([
        FirebaseService.getReviews(tourId),
        this.getTourAvailability(tourId)
      ]);

      return {
        tour,
        reviews,
        availability
      };
    } catch (error) {
      console.error('Error getting tour details:', error);
      throw error;
    }
  }

  // Get tour availability
  static async getTourAvailability(tourId: string, months: number = 3) {
    try {
      const startDate = new Date().toISOString().split('T')[0];
      const endDate = new Date(Date.now() + months * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      return await FirebaseService.getTourAvailability(tourId, startDate, endDate);
    } catch (error) {
      console.error('Error getting tour availability:', error);
      throw error;
    }
  }

  // Create custom tour from tour builder
  static async createCustomTour(tourData: any) {
    try {
      const customTour = {
        id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: tourData.title || 'Custom Safari Tour',
        description: tourData.description || 'A personalized safari experience tailored to your preferences.',
        price: tourData.price || 2500,
        duration: tourData.duration || '7 days',
        location: tourData.destinations?.[0] || 'Tanzania',
        destinations: tourData.destinations || ['Serengeti'],
        activities: tourData.activities || ['Game Drives'],
        accommodations: tourData.accommodations || ['Mid-range Lodge'],
        maxGroupSize: 12,
        minGroupSize: 1,
        difficulty: 'moderate' as const,
        includes: [
          'Professional safari guide',
          'Transportation in safari vehicle',
          'Park entrance fees',
          'Meals as specified',
          'Drinking water during game drives'
        ],
        excludes: [
          'International flights',
          'Travel insurance',
          'Personal expenses',
          'Tips and gratuities'
        ],
        images: ['/placeholder.svg'],
        featured: false,
        status: 'active' as const,
        rating: 4.5,
        reviewCount: 0,
        tourType: 'standard' as const,
        seasonality: {
          greenSeason: true,
          drySeason: true,
          bestMonths: ['Jun', 'Jul', 'Aug', 'Sep', 'Oct']
        },
        itinerary: this.generateCustomItinerary(tourData),
        fitnessRequirements: {
          level: 'moderate' as const,
          description: 'Suitable for most fitness levels',
          walkingDistance: '2-5 km per day',
          terrain: 'Mostly vehicle-based with short walks',
          ageRestrictions: 'Suitable for all ages',
          medicalConditions: []
        },
        equipment: {
          provided: [
            { name: 'Safari Vehicle', description: '4x4 vehicle with pop-up roof', category: 'gear', optional: false },
            { name: 'Binoculars', description: 'High-quality binoculars for wildlife viewing', category: 'gear', optional: false }
          ],
          recommended: [
            { name: 'Sun Hat', description: 'Wide-brimmed hat for sun protection', category: 'clothing', optional: true },
            { name: 'Camera', description: 'DSLR or mirrorless camera', category: 'photography', optional: true }
          ],
          required: [
            { name: 'Passport', description: 'Valid passport with 6+ months validity', category: 'gear', optional: false }
          ]
        },
        groupOptions: [
          {
            type: 'private' as const,
            minParticipants: 1,
            maxParticipants: 12,
            pricePerPerson: tourData.price || 2500,
            description: 'Private tour with dedicated guide'
          }
        ],
        specialFeatures: tourData.specialFeatures || ['Custom Itinerary', 'Flexible Schedule'],
        difficultyDetails: 'This custom tour is designed to match your preferred activity level and interests.',
        customData: {
          builderData: tourData,
          createdAt: new Date().toISOString()
        }
      };

      // Store in session storage for booking process
      sessionStorage.setItem('customTour', JSON.stringify(customTour));
      
      return customTour;
    } catch (error) {
      console.error('Error creating custom tour:', error);
      throw error;
    }
  }

  // Generate itinerary for custom tour
  private static generateCustomItinerary(tourData: any) {
    const days = parseInt(tourData.duration?.split(' ')[0]) || 7;
    const destinations = tourData.destinations || ['Serengeti'];
    const activities = tourData.activities || ['Game Drives'];
    
    return Array.from({ length: days }, (_, index) => ({
      day: index + 1,
      title: `Day ${index + 1}: ${destinations[index % destinations.length]}`,
      description: `Explore ${destinations[index % destinations.length]} with exciting ${activities[index % activities.length].toLowerCase()}.`,
      accommodation: tourData.accommodations?.[0] || 'Safari Lodge',
      meals: ['Breakfast', 'Lunch', 'Dinner'],
      activities: [
        {
          time: '06:00',
          activity: activities[index % activities.length],
          description: `Morning ${activities[index % activities.length].toLowerCase()} in ${destinations[index % destinations.length]}`,
          duration: '3 hours',
          location: destinations[index % destinations.length]
        },
        {
          time: '16:00',
          activity: activities[(index + 1) % activities.length],
          description: `Afternoon ${activities[(index + 1) % activities.length].toLowerCase()}`,
          duration: '2 hours',
          location: destinations[index % destinations.length]
        }
      ],
      drivingTime: index === 0 ? '2 hours from airport' : '1-2 hours between locations',
      highlights: [
        `Wildlife viewing in ${destinations[index % destinations.length]}`,
        'Professional photography opportunities',
        'Cultural interactions with local communities'
      ]
    }));
  }

  // Get tour statistics
  static async getTourStatistics() {
    try {
      return await FirebaseService.getTourStats();
    } catch (error) {
      console.error('Error getting tour statistics:', error);
      throw error;
    }
  }
}
