
import React from 'react';
import { <PERSON>, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Star, Clock, MapPin, Heart } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Tour {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  image: string;
  category: string;
  accommodationLevel: string;
  destinations: string[];
  rating: number;
  reviews: number;
}

interface TourCardProps {
  tour: Tour;
  viewMode: 'grid' | 'list';
}

const TourCard: React.FC<TourCardProps> = ({ tour, viewMode }) => {
  if (viewMode === 'list') {
    return (
      <Card className="overflow-hidden hover:shadow-lg transition-shadow">
        <div className="flex flex-col md:flex-row">
          <div className="md:w-1/3">
            <img
              src={`https://images.unsplash.com/${tour.image}?auto=format&fit=crop&w=400&h=250`}
              alt={tour.title}
              className="w-full h-48 md:h-full object-cover"
            />
          </div>
          <div className="md:w-2/3 p-6">
            <div className="flex justify-between items-start mb-2">
              <div>
                <Badge variant="secondary" className="mb-2">
                  {tour.category}
                </Badge>
                <h3 className="text-xl font-semibold mb-2">{tour.title}</h3>
              </div>
              <Button variant="ghost" size="icon">
                <Heart className="h-4 w-4" />
              </Button>
            </div>
            
            <p className="text-gray-600 mb-4 line-clamp-2">{tour.description}</p>
            
            <div className="flex flex-wrap gap-4 mb-4 text-sm text-gray-500">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                {tour.duration}
              </div>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                {tour.destinations.join(', ')}
              </div>
              <div className="flex items-center">
                <Star className="h-4 w-4 mr-1 text-yellow-500" />
                {tour.rating} ({tour.reviews} reviews)
              </div>
            </div>
            
            <div className="flex justify-between items-center">
              <div>
                <span className="text-2xl font-bold text-orange-600">
                  ${tour.price.toLocaleString()}
                </span>
                <span className="text-gray-500 ml-1">per person</span>
              </div>
              <div className="flex gap-2">
                <Link to={`/tours/${tour.id}`}>
                  <Button variant="outline">View Details</Button>
                </Link>
                <Link to={`/book/${tour.id}`}>
                  <Button className="bg-orange-600 hover:bg-orange-700">Book Now</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow group">
      <div className="relative">
        <img
          src={`https://images.unsplash.com/${tour.image}?auto=format&fit=crop&w=400&h=250`}
          alt={tour.title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <Button 
          variant="ghost" 
          size="icon" 
          className="absolute top-2 right-2 bg-white/80 hover:bg-white"
        >
          <Heart className="h-4 w-4" />
        </Button>
        <Badge className="absolute bottom-2 left-2 bg-orange-600">
          {tour.category}
        </Badge>
      </div>
      
      <CardContent className="p-4">
        <h3 className="text-lg font-semibold mb-2 line-clamp-1">{tour.title}</h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{tour.description}</p>
        
        <div className="space-y-2 text-sm text-gray-500">
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            {tour.duration}
          </div>
          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-2" />
            <span className="line-clamp-1">{tour.destinations.join(', ')}</span>
          </div>
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-2 text-yellow-500" />
            {tour.rating} ({tour.reviews} reviews)
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="p-4 pt-0">
        <div className="w-full">
          <div className="flex justify-between items-center mb-3">
            <div>
              <span className="text-xl font-bold text-orange-600">
                ${tour.price.toLocaleString()}
              </span>
              <span className="text-gray-500 text-sm ml-1">per person</span>
            </div>
            <Badge variant="outline">{tour.accommodationLevel}</Badge>
          </div>
          
          <div className="flex gap-2">
            <Link to={`/tours/${tour.id}`} className="flex-1">
              <Button variant="outline" className="w-full">View Details</Button>
            </Link>
            <Link to={`/book/${tour.id}`} className="flex-1">
              <Button className="w-full bg-orange-600 hover:bg-orange-700">Book</Button>
            </Link>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
};

export default TourCard;
