
import React, { useEffect, useRef } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Shield, Award, Users, Heart, Clock, Star, Camera, MapPin, Globe } from 'lucide-react';

const About = () => {
  const parallaxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (parallaxRef.current) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        parallaxRef.current.style.transform = `translateY(${rate}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Founder & Safari Director',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?auto=format&fit=crop&w=400&h=400',
      bio: 'With over 20 years of experience in Tanzanian wildlife conservation and tourism, Sarah founded SafariSole to create authentic and sustainable safari experiences.',
      achievements: ['Wildlife Conservation Expert', 'Licensed Safari Guide', 'Published Author'],
      social: { linkedin: '#', twitter: '#' }
    },
    {
      name: 'David Mwasumbi',
      role: 'Head Safari Guide',
      image: 'https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f?auto=format&fit=crop&w=400&h=400',
      bio: 'Born and raised near Serengeti, David brings unparalleled knowledge of wildlife behavior and tracking skills, with certification in wildlife biology.',
      achievements: ['Master Tracker', 'Wildlife Biologist', 'Cultural Ambassador'],
      social: { linkedin: '#', instagram: '#' }
    },
    {
      name: 'Emily Waters',
      role: 'Conservation Director',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=400&h=400',
      bio: 'A passionate conservationist with a PhD in Wildlife Ecology, Emily ensures our safaris contribute positively to wildlife conservation and community development.',
      achievements: ['PhD Wildlife Ecology', 'Conservation Research', 'Community Development'],
      social: { linkedin: '#', twitter: '#' }
    }
  ];

  const stats = [
    { label: 'Years of Experience', value: '15+', icon: Clock },
    { label: 'Happy Travelers', value: '5,000+', icon: Users },
    { label: 'Safari Tours', value: '200+', icon: MapPin },
    { label: 'Conservation Projects', value: '25+', icon: Heart }
  ];

  const milestones = [
    { year: '2009', title: 'Company Founded', description: 'SafariSole Tours was established with a vision to create sustainable safari experiences.' },
    { year: '2012', title: 'Conservation Partnership', description: 'Partnered with Tanzania Wildlife Authority for conservation initiatives.' },
    { year: '2015', title: 'International Recognition', description: 'Received Responsible Tourism Award from Tanzania Tourism Board.' },
    { year: '2018', title: 'Community Programs', description: 'Launched community development programs in rural Tanzania.' },
    { year: '2022', title: 'Digital Innovation', description: 'Introduced virtual tours and AI-powered trip planning.' },
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section with Parallax */}
        <div className="relative h-screen overflow-hidden">
          <div 
            ref={parallaxRef}
            className="absolute inset-0 w-full h-120"
            style={{
              backgroundImage: 'url(https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=1920&h=1080)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundAttachment: 'fixed'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-black/20 to-black/60" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-4xl mx-auto">
              <Badge className="mb-6 bg-orange-600 text-white px-4 py-2 text-lg">
                <Globe className="w-4 h-4 mr-2" />
                Est. 2009
              </Badge>
              <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
                Our <span className="text-orange-400">Story</span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90">
                Creating unforgettable safari experiences in Tanzania for over 15 years
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                {stats.map((stat, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-md rounded-lg p-4 min-w-32">
                    <stat.icon className="w-6 h-6 mx-auto mb-2 text-orange-400" />
                    <div className="text-2xl font-bold">{stat.value}</div>
                    <div className="text-sm opacity-80">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Company Story */}
        <div className="container mx-auto px-4 py-20">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div>
                <Badge className="mb-4 bg-orange-100 text-orange-800">Our Mission</Badge>
                <h2 className="text-4xl font-bold mb-6 text-gray-900">About SafariSole</h2>
                <div className="prose prose-lg text-gray-600 space-y-4">
                  <p>
                    Founded in 2009, SafariSole Tours was born from a deep passion for Tanzania's wildlife and a commitment to sustainable tourism. Our founder, Sarah Johnson, started the company after spending a decade working in wildlife conservation across East Africa.
                  </p>
                  <p>
                    The name "SafariSole" represents our belief that every safari journey should feel personally tailored, as comfortable as your favorite pair of shoes, yet adventurous enough to leave footprints in your memory forever.
                  </p>
                  <p>
                    Today, we are a team of over 30 dedicated safari professionals, including expert guides, conservation specialists, and hospitality experts. Together, we create experiences that connect our guests with the spectacular landscapes and wildlife of Tanzania while supporting local communities and conservation efforts.
                  </p>
                </div>
                <div className="mt-8 flex gap-4">
                  <Button className="bg-orange-600 hover:bg-orange-700">
                    <Camera className="mr-2 h-5 w-5" />
                    View Gallery
                  </Button>
                  <Button variant="outline" className="border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white">
                    Our Impact Report
                  </Button>
                </div>
              </div>
              <div className="relative">
                <div className="grid grid-cols-2 gap-4">
                  <img
                    src="https://images.unsplash.com/photo-1493962853295-0fd70327578a?auto=format&fit=crop&w=400&h=300"
                    alt="Safari Wildlife"
                    className="rounded-lg shadow-lg hover:scale-105 transition-transform duration-300"
                  />
                  <img
                    src="https://images.unsplash.com/photo-1466721591366-2d5fba72006d?auto=format&fit=crop&w=400&h=300"
                    alt="Safari Landscape"
                    className="rounded-lg shadow-lg hover:scale-105 transition-transform duration-300 mt-8"
                  />
                </div>
                <div className="absolute -bottom-4 -left-4 bg-white rounded-lg shadow-xl p-4">
                  <div className="flex items-center space-x-2">
                    <Star className="h-5 w-5 text-yellow-400 fill-current" />
                    <span className="font-semibold">4.9/5</span>
                    <span className="text-gray-500">from 2,000+ reviews</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Timeline */}
        <div className="bg-gradient-to-b from-gray-50 to-white py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-orange-100 text-orange-800">Our Journey</Badge>
              <h2 className="text-4xl font-bold mb-4">Company Milestones</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                From a small startup to Tanzania's leading safari operator
              </p>
            </div>
            
            <div className="max-w-4xl mx-auto">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-start mb-12 last:mb-0">
                  <div className="flex-shrink-0 w-24 text-right mr-8">
                    <Badge className="bg-orange-600 text-white text-lg px-3 py-1">
                      {milestone.year}
                    </Badge>
                  </div>
                  <div className="flex-shrink-0 w-4 h-4 bg-orange-600 rounded-full mt-2 mr-8 relative">
                    {index !== milestones.length - 1 && (
                      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-0.5 h-16 bg-orange-200"></div>
                    )}
                  </div>
                  <div className="flex-1 pb-8">
                    <h3 className="text-xl font-semibold mb-2">{milestone.title}</h3>
                    <p className="text-gray-600">{milestone.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Values */}
        <div className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-orange-100 text-orange-800">Our Values</Badge>
              <h2 className="text-4xl font-bold mb-4">What Drives Us</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                The principles that guide every safari experience we create
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                <CardContent className="p-8 text-center">
                  <div className="bg-gradient-to-br from-orange-100 to-red-100 p-4 rounded-full mb-6 inline-block group-hover:scale-110 transition-transform">
                    <Heart className="h-8 w-8 text-orange-600" />
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">Authentic Experiences</h3>
                  <p className="text-gray-600 leading-relaxed">
                    We believe in real, unscripted connections with nature, wildlife, and local cultures. Every moment is genuine and meaningful.
                  </p>
                </CardContent>
              </Card>
              
              <Card className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                <CardContent className="p-8 text-center">
                  <div className="bg-gradient-to-br from-green-100 to-emerald-100 p-4 rounded-full mb-6 inline-block group-hover:scale-110 transition-transform">
                    <Shield className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">Conservation Focus</h3>
                  <p className="text-gray-600 leading-relaxed">
                    We actively contribute to wildlife conservation and ecosystem protection, ensuring future generations can enjoy Tanzania's natural wonders.
                  </p>
                </CardContent>
              </Card>
              
              <Card className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                <CardContent className="p-8 text-center">
                  <div className="bg-gradient-to-br from-blue-100 to-cyan-100 p-4 rounded-full mb-6 inline-block group-hover:scale-110 transition-transform">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">Community Support</h3>
                  <p className="text-gray-600 leading-relaxed">
                    We partner with local communities, ensuring tourism benefits those who call Tanzania home and preserves cultural heritage.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="bg-gradient-to-b from-gray-50 to-white py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-orange-100 text-orange-800">Our Team</Badge>
              <h2 className="text-4xl font-bold mb-4">Meet Our Experts</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Passionate professionals dedicated to creating extraordinary safari experiences
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {teamMembers.map((member, index) => (
                <Card key={index} className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                  <CardContent className="p-0 overflow-hidden">
                    <div className="relative">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="absolute bottom-4 left-4 right-4 text-white">
                          <div className="flex space-x-2">
                            {member.social.linkedin && (
                              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                                LinkedIn
                              </Button>
                            )}
                            {member.social.twitter && (
                              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                                Twitter
                              </Button>
                            )}
                            {member.social.instagram && (
                              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                                Instagram
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                      <p className="text-orange-600 font-medium mb-3">{member.role}</p>
                      <p className="text-gray-600 mb-4 text-sm leading-relaxed">{member.bio}</p>
                      <div className="space-y-1">
                        {member.achievements.map((achievement, i) => (
                          <Badge key={i} variant="outline" className="text-xs mr-1 mb-1">
                            {achievement}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Credentials */}
        <div className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-orange-100 text-orange-800">Credentials</Badge>
              <h2 className="text-4xl font-bold mb-4">Our Certifications</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Trusted by industry leaders and certified by governing bodies
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-100">
                <Award className="h-12 w-12 text-orange-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-center mb-2">Tanzania Tourism Board</h3>
                <p className="text-sm text-gray-600 text-center">Licensed Tour Operator #TLO-2543</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-100">
                <Shield className="h-12 w-12 text-orange-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-center mb-2">TATO Member</h3>
                <p className="text-sm text-gray-600 text-center">Tanzania Association of Tour Operators</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-100">
                <Users className="h-12 w-12 text-orange-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-center mb-2">Responsible Tourism</h3>
                <p className="text-sm text-gray-600 text-center">Certified Eco-Tourism Provider</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-100">
                <Clock className="h-12 w-12 text-orange-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-center mb-2">15+ Years Experience</h3>
                <p className="text-sm text-gray-600 text-center">Thousands of successful safaris</p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default About;
