
import React, { useEffect, useRef } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Shield, Award, Users, Heart, Clock, Star, Camera, MapPin, Globe } from 'lucide-react';

const About = () => {
  const parallaxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (parallaxRef.current) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;
        parallaxRef.current.style.transform = `translateY(${rate}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Founder & Safari Director',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?auto=format&fit=crop&w=400&h=400',
      bio: 'With over 20 years of experience in Tanzanian wildlife conservation and tourism, Sarah founded SafariSole to create authentic and sustainable safari experiences.',
      achievements: ['Wildlife Conservation Expert', 'Licensed Safari Guide', 'Published Author'],
      social: { linkedin: '#', twitter: '#' }
    },
    {
      name: 'David Mwasumbi',
      role: 'Head Safari Guide',
      image: 'https://images.unsplash.com/photo-1506277886164-e25aa3f4ef7f?auto=format&fit=crop&w=400&h=400',
      bio: 'Born and raised near Serengeti, David brings unparalleled knowledge of wildlife behavior and tracking skills, with certification in wildlife biology.',
      achievements: ['Master Tracker', 'Wildlife Biologist', 'Cultural Ambassador'],
      social: { linkedin: '#', instagram: '#' }
    },
    {
      name: 'Emily Waters',
      role: 'Conservation Director',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=400&h=400',
      bio: 'A passionate conservationist with a PhD in Wildlife Ecology, Emily ensures our safaris contribute positively to wildlife conservation and community development.',
      achievements: ['PhD Wildlife Ecology', 'Conservation Research', 'Community Development'],
      social: { linkedin: '#', twitter: '#' }
    }
  ];

  const stats = [
    { label: 'Years of Experience', value: '15+', icon: Clock },
    { label: 'Happy Travelers', value: '5,000+', icon: Users },
    { label: 'Safari Tours', value: '200+', icon: MapPin },
    { label: 'Conservation Projects', value: '25+', icon: Heart }
  ];

  const milestones = [
    { year: '2009', title: 'Company Founded', description: 'SafariSole Tours was established with a vision to create sustainable safari experiences.' },
    { year: '2012', title: 'Conservation Partnership', description: 'Partnered with Tanzania Wildlife Authority for conservation initiatives.' },
    { year: '2015', title: 'International Recognition', description: 'Received Responsible Tourism Award from Tanzania Tourism Board.' },
    { year: '2018', title: 'Community Programs', description: 'Launched community development programs in rural Tanzania.' },
    { year: '2022', title: 'Digital Innovation', description: 'Introduced virtual tours and AI-powered trip planning.' },
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Enhanced Hero Section with Savannah Theme */}
        <div className="relative h-screen overflow-hidden bg-horizon-gradient">
          <div
            ref={parallaxRef}
            className="absolute inset-0 w-full h-120"
            style={{
              backgroundImage: 'url(https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=1920&h=1080)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundAttachment: 'fixed'
            }}
          />
          {/* Enhanced gradient overlay with savannah colors */}
          <div className="absolute inset-0 bg-gradient-to-b from-savannah-900/40 via-sunset-900/20 to-acacia-900/60" />
          {/* Savannah texture overlay */}
          <div className="absolute inset-0 bg-savannah-texture opacity-20" />

          {/* Animated savannah elements */}
          <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-savannah-800/30 to-transparent">
            <div className="absolute bottom-0 left-1/4 w-2 h-16 bg-savannah-700/40 rounded-t-full animate-grass-sway"></div>
            <div className="absolute bottom-0 right-1/4 w-2 h-20 bg-savannah-700/40 rounded-t-full animate-grass-sway-delayed"></div>
          </div>

          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-4xl mx-auto">
              {/* Wildlife icons animation */}
              <div className="flex justify-center space-x-6 mb-6 opacity-80">
                <div className="text-2xl animate-wildlife-emerge">🦁</div>
                <div className="text-2xl animate-wildlife-emerge" style={{animationDelay: '0.3s'}}>🐘</div>
                <div className="text-2xl animate-wildlife-emerge" style={{animationDelay: '0.6s'}}>🦒</div>
              </div>

              <Badge className="mb-6 bg-gradient-to-r from-wildlife-lion to-sunset-600 text-white px-6 py-3 text-lg font-bold rounded-xl shadow-xl animate-wildlife-emerge">
                <Globe className="w-5 h-5 mr-2" />
                🌅 Est. 2009
              </Badge>

              <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-sun-rise font-safari">
                Our <span className="text-wildlife-gold bg-gradient-to-r from-wildlife-lion via-sunset-400 to-wildlife-cheetah bg-clip-text text-transparent">Story</span>
              </h1>

              <p className="text-xl md:text-2xl mb-8 leading-relaxed opacity-90 font-adventure animate-fade-in">
                🌍 Creating unforgettable safari experiences in Tanzania for over 15 years
              </p>

              {/* Enhanced stats with savannah styling */}
              <div className="flex flex-wrap justify-center gap-4">
                {stats.map((stat, index) => (
                  <div key={index} className="card-wildlife backdrop-blur-md rounded-xl p-6 min-w-36 hover-wildlife animate-wildlife-emerge" style={{animationDelay: `${index * 0.2}s`}}>
                    <stat.icon className="w-8 h-8 mx-auto mb-3 text-wildlife-lion" />
                    <div className="text-2xl font-bold text-savannah-800">{stat.value}</div>
                    <div className="text-sm text-savannah-600 font-adventure">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Company Story with Savannah Theme */}
        <div className="container mx-auto px-4 py-20 bg-savannah-texture">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div className="animate-fade-in">
                <Badge className="mb-4 bg-gradient-to-r from-savannah-100 to-sunset-100 text-savannah-800 px-4 py-2 rounded-xl font-bold">
                  🎯 Our Mission
                </Badge>
                <h2 className="text-4xl md:text-5xl font-bold mb-6 text-savannah-900 font-safari">
                  About <span className="text-wildlife-gold">SafariSole</span>
                </h2>

                {/* Decorative divider */}
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-px bg-gradient-to-r from-wildlife-lion to-sunset-500"></div>
                  <div className="text-sunset-500 text-xl">🌅</div>
                  <div className="w-12 h-px bg-gradient-to-l from-wildlife-lion to-sunset-500"></div>
                </div>

                <div className="prose prose-lg text-savannah-700 space-y-6 font-adventure">
                  <p className="text-lg leading-relaxed">
                    🌍 Founded in 2009, SafariSole Tours was born from a deep passion for Tanzania's wildlife and a commitment to sustainable tourism. Our founder, Sarah Johnson, started the company after spending a decade working in wildlife conservation across East Africa.
                  </p>
                  <p className="text-lg leading-relaxed">
                    👟 The name "SafariSole" represents our belief that every safari journey should feel personally tailored, as comfortable as your favorite pair of shoes, yet adventurous enough to leave footprints in your memory forever.
                  </p>
                  <p className="text-lg leading-relaxed">
                    🤝 Today, we are a team of over 30 dedicated safari professionals, including expert guides, conservation specialists, and hospitality experts. Together, we create experiences that connect our guests with the spectacular landscapes and wildlife of Tanzania while supporting local communities and conservation efforts.
                  </p>
                </div>

                <div className="mt-10 flex flex-col sm:flex-row gap-4">
                  <Button variant="wildlife" size="lg" className="group">
                    <Camera className="mr-2 h-5 w-5 group-hover:animate-animal-walk" />
                    📸 View Gallery
                  </Button>
                  <Button variant="savannah-outline" size="lg" className="group">
                    📊 Our Impact Report
                  </Button>
                </div>
              </div>
              <div className="relative animate-slide-in-right">
                <div className="grid grid-cols-2 gap-6">
                  <div className="relative group">
                    <img
                      src="https://images.unsplash.com/photo-1493962853295-0fd70327578a?auto=format&fit=crop&w=400&h=300"
                      alt="Safari Wildlife"
                      className="rounded-2xl shadow-xl hover:scale-105 transition-all duration-500 border-4 border-savannah-200 group-hover:border-sunset-300"
                    />
                    {/* Wildlife overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-savannah-900/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-4 left-4 text-white font-bold">🦁 Big Five</div>
                    </div>
                  </div>

                  <div className="relative group mt-8">
                    <img
                      src="https://images.unsplash.com/photo-1466721591366-2d5fba72006d?auto=format&fit=crop&w=400&h=300"
                      alt="Safari Landscape"
                      className="rounded-2xl shadow-xl hover:scale-105 transition-all duration-500 border-4 border-acacia-200 group-hover:border-sunset-300"
                    />
                    {/* Landscape overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-sunset-900/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-4 left-4 text-white font-bold">🌅 Serengeti</div>
                    </div>
                  </div>
                </div>

                {/* Enhanced rating card */}
                <div className="absolute -bottom-6 -left-6 card-wildlife p-6 animate-wildlife-emerge">
                  <div className="flex items-center space-x-3">
                    <div className="flex space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-sunset-500 fill-current animate-bounce-in" style={{animationDelay: `${i * 0.1}s`}} />
                      ))}
                    </div>
                    <div>
                      <div className="font-bold text-savannah-900 text-lg">4.9/5</div>
                      <div className="text-savannah-600 text-sm font-adventure">from 2,000+ reviews</div>
                    </div>
                  </div>
                </div>

                {/* Floating wildlife elements */}
                <div className="absolute top-4 right-4 text-2xl opacity-60 animate-wildlife-emerge">🦒</div>
                <div className="absolute bottom-20 right-8 text-xl opacity-40 animate-wildlife-emerge" style={{animationDelay: '1s'}}>🌳</div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Timeline with Savannah Theme */}
        <div className="bg-gradient-to-b from-savannah-50 via-acacia-50 to-white py-20 bg-wildlife-pattern">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16 animate-fade-in">
              <Badge className="mb-4 bg-gradient-to-r from-sunset-100 to-savannah-100 text-savannah-800 px-6 py-3 rounded-xl font-bold text-lg">
                🛤️ Our Journey
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-4 text-savannah-900 font-safari">
                Company <span className="text-wildlife-gold">Milestones</span>
              </h2>
              <p className="text-xl text-savannah-700 max-w-2xl mx-auto font-adventure">
                🌱 From a small startup to Tanzania's leading safari operator
              </p>

              {/* Decorative elements */}
              <div className="flex justify-center items-center space-x-4 mt-6">
                <div className="w-16 h-px bg-gradient-to-r from-transparent to-wildlife-lion"></div>
                <div className="text-sunset-500 text-2xl">🌍</div>
                <div className="w-16 h-px bg-gradient-to-l from-transparent to-wildlife-lion"></div>
              </div>
            </div>

            <div className="max-w-4xl mx-auto relative">
              {/* Enhanced timeline line */}
              <div className="absolute left-32 top-0 bottom-0 w-1 bg-gradient-to-b from-wildlife-lion via-sunset-500 to-wildlife-cheetah rounded-full"></div>

              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-start mb-16 last:mb-0 animate-slide-in-left" style={{animationDelay: `${index * 0.2}s`}}>
                  <div className="flex-shrink-0 w-28 text-right mr-8">
                    <Badge className="bg-gradient-to-r from-wildlife-lion to-sunset-600 text-white text-lg px-4 py-2 rounded-xl font-bold shadow-lg hover:scale-105 transition-transform">
                      {milestone.year}
                    </Badge>
                  </div>

                  {/* Enhanced timeline dot */}
                  <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-sunset-500 to-wildlife-cheetah rounded-full mt-2 mr-8 relative shadow-lg border-4 border-white hover:scale-125 transition-transform">
                    {index !== milestones.length - 1 && (
                      <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-1 h-20 bg-gradient-to-b from-sunset-300 to-savannah-300 rounded-full"></div>
                    )}
                    {/* Pulsing effect */}
                    <div className="absolute inset-0 bg-sunset-400 rounded-full animate-ping opacity-20"></div>
                  </div>

                  <div className="flex-1 pb-8">
                    <div className="card-wildlife p-6 hover-wildlife">
                      <h3 className="text-xl font-bold mb-3 text-savannah-900 font-safari flex items-center">
                        <span className="mr-2">🎯</span>
                        {milestone.title}
                      </h3>
                      <p className="text-savannah-700 leading-relaxed font-adventure">{milestone.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Values Section with Wildlife Theme */}
        <div className="py-20 bg-savannah-texture">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16 animate-fade-in">
              <Badge className="mb-4 bg-gradient-to-r from-acacia-100 to-baobab-100 text-acacia-800 px-6 py-3 rounded-xl font-bold text-lg">
                💎 Our Values
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-4 text-savannah-900 font-safari">
                What <span className="text-wildlife-gold">Drives Us</span>
              </h2>
              <p className="text-xl text-savannah-700 max-w-2xl mx-auto font-adventure">
                🌟 The principles that guide every safari experience we create
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Authentic Experiences */}
              <Card variant="wildlife" className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-4 animate-wildlife-emerge">
                <CardContent className="p-8 text-center">
                  <div className="bg-gradient-to-br from-sunset-100 to-wildlife-lion/20 p-6 rounded-2xl mb-6 inline-block group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg">
                    <Heart className="h-10 w-10 text-sunset-600 animate-animal-walk" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-savannah-900 font-safari">
                    🎭 Authentic Experiences
                  </h3>
                  <p className="text-savannah-700 leading-relaxed font-adventure">
                    We believe in real, unscripted connections with nature, wildlife, and local cultures. Every moment is genuine and meaningful.
                  </p>
                  {/* Decorative element */}
                  <div className="mt-4 flex justify-center">
                    <div className="w-12 h-px bg-gradient-to-r from-transparent via-sunset-400 to-transparent"></div>
                  </div>
                </CardContent>
              </Card>

              {/* Conservation Focus */}
              <Card variant="wildlife" className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-4 animate-wildlife-emerge" style={{animationDelay: '0.2s'}}>
                <CardContent className="p-8 text-center">
                  <div className="bg-gradient-to-br from-baobab-100 to-acacia-200 p-6 rounded-2xl mb-6 inline-block group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg">
                    <Shield className="h-10 w-10 text-baobab-600 animate-acacia-grow" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-savannah-900 font-safari">
                    🛡️ Conservation Focus
                  </h3>
                  <p className="text-savannah-700 leading-relaxed font-adventure">
                    We actively contribute to wildlife conservation and ecosystem protection, ensuring future generations can enjoy Tanzania's natural wonders.
                  </p>
                  <div className="mt-4 flex justify-center">
                    <div className="w-12 h-px bg-gradient-to-r from-transparent via-baobab-400 to-transparent"></div>
                  </div>
                </CardContent>
              </Card>

              {/* Community Support */}
              <Card variant="wildlife" className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-4 animate-wildlife-emerge" style={{animationDelay: '0.4s'}}>
                <CardContent className="p-8 text-center">
                  <div className="bg-gradient-to-br from-savannah-100 to-wildlife-cheetah/20 p-6 rounded-2xl mb-6 inline-block group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg">
                    <Users className="h-10 w-10 text-wildlife-cheetah animate-sun-rise" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-savannah-900 font-safari">
                    🤝 Community Support
                  </h3>
                  <p className="text-savannah-700 leading-relaxed font-adventure">
                    We partner with local communities, ensuring tourism benefits those who call Tanzania home and preserves cultural heritage.
                  </p>
                  <div className="mt-4 flex justify-center">
                    <div className="w-12 h-px bg-gradient-to-r from-transparent via-wildlife-cheetah to-transparent"></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="bg-gradient-to-b from-gray-50 to-white py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-orange-100 text-orange-800">Our Team</Badge>
              <h2 className="text-4xl font-bold mb-4">Meet Our Experts</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Passionate professionals dedicated to creating extraordinary safari experiences
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {teamMembers.map((member, index) => (
                <Card key={index} className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                  <CardContent className="p-0 overflow-hidden">
                    <div className="relative">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="absolute bottom-4 left-4 right-4 text-white">
                          <div className="flex space-x-2">
                            {member.social.linkedin && (
                              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                                LinkedIn
                              </Button>
                            )}
                            {member.social.twitter && (
                              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                                Twitter
                              </Button>
                            )}
                            {member.social.instagram && (
                              <Button size="sm" variant="ghost" className="text-white hover:bg-white/20">
                                Instagram
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                      <p className="text-orange-600 font-medium mb-3">{member.role}</p>
                      <p className="text-gray-600 mb-4 text-sm leading-relaxed">{member.bio}</p>
                      <div className="space-y-1">
                        {member.achievements.map((achievement, i) => (
                          <Badge key={i} variant="outline" className="text-xs mr-1 mb-1">
                            {achievement}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Credentials */}
        <div className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge className="mb-4 bg-orange-100 text-orange-800">Credentials</Badge>
              <h2 className="text-4xl font-bold mb-4">Our Certifications</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Trusted by industry leaders and certified by governing bodies
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-100">
                <Award className="h-12 w-12 text-orange-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-center mb-2">Tanzania Tourism Board</h3>
                <p className="text-sm text-gray-600 text-center">Licensed Tour Operator #TLO-2543</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-100">
                <Shield className="h-12 w-12 text-orange-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-center mb-2">TATO Member</h3>
                <p className="text-sm text-gray-600 text-center">Tanzania Association of Tour Operators</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-100">
                <Users className="h-12 w-12 text-orange-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-center mb-2">Responsible Tourism</h3>
                <p className="text-sm text-gray-600 text-center">Certified Eco-Tourism Provider</p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-shadow border border-gray-100">
                <Clock className="h-12 w-12 text-orange-600 mb-4 mx-auto" />
                <h3 className="font-semibold text-center mb-2">15+ Years Experience</h3>
                <p className="text-sm text-gray-600 text-center">Thousands of successful safaris</p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default About;
