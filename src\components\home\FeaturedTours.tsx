
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, Clock, Users, MapPin, Heart, Camera, ArrowRight } from 'lucide-react';
import WishlistButton from '@/components/features/WishlistButton';

const FeaturedTours = () => {
  const tours = [
    {
      id: '1',
      title: 'Serengeti Great Migration Safari',
      description: 'Witness the spectacular wildebeest migration in the world-famous Serengeti National Park',
      image: 'photo-1472396961693-142e6e269027',
      duration: '7 Days',
      groupSize: '6 people max',
      price: 2899,
      originalPrice: 3299,
      rating: 4.9,
      reviews: 127,
      highlights: ['Great Migration', 'Big Five', 'Luxury Lodge'],
      badge: 'Best Seller',
      type: 'tour' as const
    },
    {
      id: '2',
      title: 'Ngorongoro Crater Adventure',
      description: 'Explore the UNESCO World Heritage site known as the "Eighth Wonder of the World"',
      image: 'photo-1466721591366-2d5fba72006d',
      duration: '5 Days',
      groupSize: '8 people max',
      price: 1999,
      originalPrice: null,
      rating: 4.8,
      reviews: 89,
      highlights: ['Crater Floor', 'Rhino Tracking', 'Cultural Visit'],
      badge: 'Popular',
      type: 'tour' as const
    },
    {
      id: '3',
      title: 'Kilimanjaro Base Safari',
      description: 'Combine wildlife viewing with stunning views of Africa\'s highest mountain',
      image: 'photo-1493962853295-0fd70327578a',
      duration: '6 Days',
      groupSize: '4 people max',
      price: 2299,
      originalPrice: 2599,
      rating: 4.7,
      reviews: 64,
      highlights: ['Kilimanjaro Views', 'Elephant Herds', 'Photography'],
      badge: 'New',
      type: 'tour' as const
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f97316' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-orange-100 text-orange-800 px-4 py-2">
            <Camera className="w-4 h-4 mr-2" />
            Featured Tours
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Featured Safari <span className="text-orange-600">Tours</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover our most popular safari experiences, carefully crafted to showcase 
            the best of Tanzania's wildlife and landscapes with expert guides and premium accommodations.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {tours.map((tour) => (
            <div
              key={tour.id}
              className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 overflow-hidden hover:-translate-y-3"
            >
              {/* Image */}
              <div className="relative overflow-hidden h-56">
                <img
                  src={`https://images.unsplash.com/${tour.image}?auto=format&fit=crop&w=500&h=300`}
                  alt={tour.title}
                  className="h-full w-full object-cover group-hover:scale-110 transition-transform duration-700"
                />
                
                {/* Badge */}
                <Badge 
                  className={`absolute top-4 left-4 px-3 py-1 text-sm font-semibold ${
                    tour.badge === 'Best Seller' ? 'bg-red-500 hover:bg-red-600' : 
                    tour.badge === 'Popular' ? 'bg-blue-500 hover:bg-blue-600' : 'bg-green-500 hover:bg-green-600'
                  } text-white shadow-lg`}
                >
                  {tour.badge}
                </Badge>

                {/* Wishlist Button */}
                <div className="absolute top-4 right-4">
                  <WishlistButton 
                    item={{
                      id: tour.id,
                      title: tour.title,
                      price: tour.price,
                      image: `https://images.unsplash.com/${tour.image}?auto=format&fit=crop&w=500&h=300`,
                      type: tour.type
                    }}
                    size="icon"
                  />
                </div>

                {/* Price Overlay */}
                <div className="absolute bottom-4 right-4 bg-white/95 backdrop-blur-sm rounded-xl px-4 py-2 shadow-lg">
                  <div className="text-right">
                    {tour.originalPrice && (
                      <span className="text-sm text-gray-500 line-through block">
                        ${tour.originalPrice}
                      </span>
                    )}
                    <div className="text-xl font-bold text-orange-600">
                      ${tour.price}
                    </div>
                    <div className="text-xs text-gray-500">per person</div>
                  </div>
                </div>

                {/* Quick View Button */}
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Button 
                    asChild
                    className="bg-white text-gray-900 hover:bg-gray-100 shadow-lg"
                  >
                    <Link to={`/tours/${tour.id}`}>
                      <Camera className="mr-2 h-4 w-4" />
                      Quick View
                    </Link>
                  </Button>
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <div className="mb-4">
                  <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors leading-tight">
                    {tour.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {tour.description}
                  </p>
                </div>

                {/* Tour Details */}
                <div className="flex items-center justify-between mb-6 text-sm">
                  <div className="flex items-center text-gray-500">
                    <Clock className="h-4 w-4 mr-2" />
                    <span className="font-medium">{tour.duration}</span>
                  </div>
                  <div className="flex items-center text-gray-500">
                    <Users className="h-4 w-4 mr-2" />
                    <span className="font-medium">{tour.groupSize}</span>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="flex items-center mr-3">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          className={`h-4 w-4 ${i < Math.floor(tour.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                        />
                      ))}
                    </div>
                    <span className="text-sm font-semibold text-gray-900 mr-1">
                      {tour.rating}
                    </span>
                    <span className="text-sm text-gray-500">
                      ({tour.reviews} reviews)
                    </span>
                  </div>
                </div>

                {/* Highlights */}
                <div className="mb-6">
                  <div className="flex flex-wrap gap-2">
                    {tour.highlights.map((highlight, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="text-xs px-3 py-1 border-orange-200 text-orange-700 bg-orange-50 hover:bg-orange-100 transition-colors"
                      >
                        {highlight}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button 
                    asChild
                    className="flex-1 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white shadow-lg hover:shadow-xl transition-all"
                  >
                    <Link to={`/tours/${tour.id}`}>
                      View Details
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                  <Button 
                    asChild
                    variant="outline" 
                    className="border-orange-600 text-orange-600 hover:bg-orange-600 hover:text-white transition-colors"
                  >
                    <Link to={`/book/${tour.id}`}>
                      Book Now
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center bg-gradient-to-r from-orange-600 to-red-600 rounded-2xl p-12 text-white">
          <h3 className="text-3xl font-bold mb-4">Ready for Your Safari Adventure?</h3>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Explore all our safari packages or create a custom itinerary tailored to your dreams
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              asChild
              size="lg" 
              variant="outline" 
              className="border-white text-white hover:bg-white hover:text-orange-600 transition-colors"
            >
              <Link to="/tours">
                <MapPin className="mr-2 h-5 w-5" />
                View All Tours
              </Link>
            </Button>
            <Button 
              asChild
              size="lg" 
              className="bg-white text-orange-600 hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all"
            >
              <Link to="/tour-builder">
                <Camera className="mr-2 h-5 w-5" />
                Create Custom Tour
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedTours;
