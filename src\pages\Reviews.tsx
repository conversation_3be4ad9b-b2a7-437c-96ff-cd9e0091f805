
import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import { Star, MapPin, Calendar, Users, Camera, Heart, ThumbsUp, MessageCircle, ExternalLink, Shield, Award } from 'lucide-react';

const Reviews = () => {
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  const reviews = [
    {
      id: 1,
      platform: 'google',
      author: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&h=150',
      rating: 5,
      date: '2024-02-10',
      location: 'United States',
      verified: true,
      tourType: 'Serengeti Safari',
      title: 'Absolutely Incredible Safari Experience!',
      content: 'This was hands down the best vacation of my life! Our guide <PERSON> was incredibly knowledgeable and spotted animals we never would have seen on our own. The accommodations were fantastic, and the whole trip was perfectly organized. We saw the Big Five and witnessed an incredible river crossing during the Great Migration. Cannot recommend enough!',
      images: ['https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=300&h=200'],
      helpful: 24,
      response: {
        author: 'Tanzania Safari Co.',
        content: 'Thank you so much for your wonderful review, Michael! We\'re thrilled that David and our team could make your safari dreams come true. Your photos from the river crossing were spectacular!',
        date: '2024-02-11'
      }
    },
    {
      id: 2,
      platform: 'tripadvisor',
      author: 'Sarah Johnson',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=150&h=150',
      rating: 5,
      date: '2024-02-08',
      location: 'United Kingdom',
      verified: true,
      tourType: 'Kilimanjaro Trek + Safari',
      title: 'Life-changing Adventure!',
      content: 'Just returned from an 8-day Kilimanjaro trek followed by a 4-day safari. The organization was flawless from start to finish. Our Kilimanjaro guides were incredibly supportive and knowledgeable about the mountain. The summit day was challenging but so rewarding! The safari afterwards was the perfect way to celebrate - we saw lions, elephants, and even leopards up close.',
      images: ['https://images.unsplash.com/photo-1589182373726-e4f658ab50f2?auto=format&fit=crop&w=300&h=200'],
      helpful: 31,
      badges: ['Top Contributor', 'Helpful']
    },
    {
      id: 3,
      platform: 'google',
      author: 'Emma Wilson',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=150&h=150',
      rating: 5,
      date: '2024-02-05',
      location: 'Australia',
      verified: true,
      tourType: 'Photography Safari',
      title: 'Perfect for Photography Enthusiasts',
      content: 'As a professional photographer, I had high expectations for this photography safari. They were exceeded in every way! The guides understood photography and positioning, the vehicles had amazing setups for camera gear, and the timing of game drives was perfect for golden hour shots. Got some award-winning shots of leopards and the Great Migration.',
      images: [
        'https://images.unsplash.com/photo-1564760055775-d63b17a55c44?auto=format&fit=crop&w=300&h=200',
        'https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=300&h=200'
      ],
      helpful: 18
    },
    {
      id: 4,
      platform: 'tripadvisor',
      author: 'James Thompson',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=150&h=150',
      rating: 5,
      date: '2024-01-28',
      location: 'Canada',
      verified: true,
      tourType: 'Family Safari',
      title: 'Amazing Family Adventure',
      content: 'Traveled with my wife and two teenagers (14 and 16). Everyone had an incredible time! The guides were fantastic with the kids, keeping them engaged and teaching them so much about wildlife and conservation. The accommodations were family-friendly, and the pace was perfect. My kids are already asking when we can go back!',
      images: ['https://images.unsplash.com/photo-1493962853295-0fd70327578a?auto=format&fit=crop&w=300&h=200'],
      helpful: 22,
      badges: ['Family Travel Expert']
    },
    {
      id: 5,
      platform: 'google',
      author: 'Lisa Martinez',
      avatar: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?auto=format&fit=crop&w=150&h=150',
      rating: 5,
      date: '2024-01-25',
      location: 'Spain',
      verified: true,
      tourType: 'Cultural Safari',
      title: 'Authentic Cultural Experience',
      content: 'This wasn\'t just about wildlife - the cultural experiences were incredible. Visiting a Maasai village and learning about their traditions was deeply moving. Our guide Grace was from the local community and shared so many personal stories. The combination of wildlife viewing and cultural immersion made this trip truly special.',
      helpful: 15,
      response: {
        author: 'Tanzania Safari Co.',
        content: 'Thank you Lisa! Grace will be so happy to read this. Supporting local communities through authentic cultural experiences is at the heart of what we do.',
        date: '2024-01-26'
      }
    },
    {
      id: 6,
      platform: 'tripadvisor',
      author: 'Robert Chen',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=150&h=150',
      rating: 5,
      date: '2024-01-20',
      location: 'Singapore',
      verified: true,
      tourType: 'Luxury Safari',
      title: 'Luxury Done Right',
      content: 'Every detail was perfect. From the private jet transfers to the exclusive camps, this was luxury travel at its finest. But what made it special was that it never felt disconnected from the authentic safari experience. Our private guide was exceptional, and having vehicles to ourselves meant we could stay with special sightings as long as we wanted.',
      images: ['https://images.unsplash.com/photo-1485833077593-4278bba3f11f?auto=format&fit=crop&w=300&h=200'],
      helpful: 28,
      badges: ['Luxury Travel Expert', 'Hotel Expert']
    }
  ];

  const stats = {
    totalReviews: 1247,
    averageRating: 4.9,
    ratingBreakdown: {
      5: 1156,
      4: 67,
      3: 15,
      2: 6,
      1: 3
    },
    platforms: {
      google: 847,
      tripadvisor: 400
    }
  };

  const awards = [
    {
      title: 'TripAdvisor Travelers\' Choice 2024',
      description: 'Top 1% of attractions worldwide',
      icon: Award,
      color: 'bg-green-600'
    },
    {
      title: 'Google 5-Star Rating',
      description: 'Consistently highest rated safari company',
      icon: Star,
      color: 'bg-yellow-500'
    },
    {
      title: 'Verified Reviews',
      description: 'All reviews verified by platform',
      icon: Shield,
      color: 'bg-blue-600'
    }
  ];

  const filteredReviews = reviews.filter(review => {
    if (filter === 'all') return true;
    return review.platform === filter;
  });

  const sortedReviews = [...filteredReviews].sort((a, b) => {
    if (sortBy === 'newest') return new Date(b.date).getTime() - new Date(a.date).getTime();
    if (sortBy === 'oldest') return new Date(a.date).getTime() - new Date(b.date).getTime();
    if (sortBy === 'highest') return b.rating - a.rating;
    if (sortBy === 'helpful') return (b.helpful || 0) - (a.helpful || 0);
    return 0;
  });

  const RatingStars = ({ rating, size = 'w-4 h-4' }: { rating: number; size?: string }) => (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`${size} ${star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
        />
      ))}
    </div>
  );

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <div className="relative h-96 overflow-hidden">
          <div 
            className="absolute inset-0 w-full h-full"
            style={{
              backgroundImage: 'url(https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=1920&h=1080)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-4xl mx-auto">
              <Badge className="mb-4 bg-orange-600 text-white px-4 py-2">
                <Star className="w-4 h-4 mr-2" />
                Customer Reviews
              </Badge>
              <h1 className="text-5xl md:text-6xl font-bold mb-6">What Our Guests Say</h1>
              <p className="text-xl md:text-2xl leading-relaxed opacity-90">
                Real experiences from thousands of satisfied safari adventurers
              </p>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="bg-white py-16">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold text-orange-600 mb-2">{stats.totalReviews.toLocaleString()}</div>
                <div className="text-gray-600">Total Reviews</div>
              </div>
              <div>
                <div className="flex items-center justify-center mb-2">
                  <span className="text-4xl font-bold text-orange-600 mr-2">{stats.averageRating}</span>
                  <RatingStars rating={5} size="w-6 h-6" />
                </div>
                <div className="text-gray-600">Average Rating</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-orange-600 mb-2">{stats.platforms.google}</div>
                <div className="text-gray-600">Google Reviews</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-orange-600 mb-2">{stats.platforms.tripadvisor}</div>
                <div className="text-gray-600">TripAdvisor Reviews</div>
              </div>
            </div>
          </div>
        </div>

        {/* Awards Section */}
        <div className="bg-gray-50 py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Awards & Recognition</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {awards.map((award, index) => (
                <Card key={index} className="text-center p-6">
                  <div className={`w-16 h-16 ${award.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                    <award.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="font-bold text-lg mb-2">{award.title}</h3>
                  <p className="text-gray-600">{award.description}</p>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Rating Breakdown */}
        <div className="bg-white py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto">
              <h2 className="text-3xl font-bold text-center mb-8">Review Breakdown</h2>
              <div className="space-y-4">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center gap-4">
                    <div className="flex items-center gap-2 w-20">
                      <span className="font-medium">{rating}</span>
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    </div>
                    <div className="flex-1 bg-gray-200 rounded-full h-3">
                      <div 
                        className="bg-orange-600 h-3 rounded-full"
                        style={{ width: `${(stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown] / stats.totalReviews) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-16 text-right">
                      {stats.ratingBreakdown[rating as keyof typeof stats.ratingBreakdown]}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Reviews Section */}
        <div className="container mx-auto px-4 py-16">
          {/* Filters */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
            <div className="flex gap-2">
              <Button
                variant={filter === 'all' ? 'default' : 'outline'}
                onClick={() => setFilter('all')}
                className={filter === 'all' ? 'bg-orange-600 hover:bg-orange-700' : ''}
              >
                All Reviews
              </Button>
              <Button
                variant={filter === 'google' ? 'default' : 'outline'}
                onClick={() => setFilter('google')}
                className={filter === 'google' ? 'bg-orange-600 hover:bg-orange-700' : ''}
              >
                Google
              </Button>
              <Button
                variant={filter === 'tripadvisor' ? 'default' : 'outline'}
                onClick={() => setFilter('tripadvisor')}
                className={filter === 'tripadvisor' ? 'bg-orange-600 hover:bg-orange-700' : ''}
              >
                TripAdvisor
              </Button>
            </div>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-md"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="highest">Highest Rated</option>
              <option value="helpful">Most Helpful</option>
            </select>
          </div>

          {/* Reviews Grid */}
          <div className="space-y-8">
            {sortedReviews.map((review) => (
              <Card key={review.id} className="overflow-hidden">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-12 w-12">
                        <img src={review.avatar} alt={review.author} className="object-cover" />
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold">{review.author}</h4>
                          {review.verified && (
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              <Shield className="w-3 h-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                          {review.badges?.map((badge) => (
                            <Badge key={badge} variant="outline" className="text-orange-600 border-orange-600">
                              {badge}
                            </Badge>
                          ))}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            {review.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(review.date).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <RatingStars rating={review.rating} />
                      <Badge 
                        variant="outline" 
                        className={review.platform === 'google' ? 'text-blue-600 border-blue-600' : 'text-green-600 border-green-600'}
                      >
                        {review.platform === 'google' ? 'Google' : 'TripAdvisor'}
                        <ExternalLink className="w-3 h-3 ml-1" />
                      </Badge>
                    </div>
                  </div>

                  <Badge variant="outline" className="mb-3">
                    {review.tourType}
                  </Badge>

                  <h3 className="font-bold text-lg mb-3">{review.title}</h3>
                  <p className="text-gray-700 leading-relaxed mb-4">{review.content}</p>

                  {review.images && review.images.length > 0 && (
                    <div className="flex gap-2 mb-4">
                      {review.images.map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`Review image ${index + 1}`}
                          className="w-20 h-20 object-cover rounded-lg"
                        />
                      ))}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Button variant="ghost" size="sm" className="text-gray-600">
                        <ThumbsUp className="w-4 h-4 mr-1" />
                        Helpful ({review.helpful})
                      </Button>
                      <Button variant="ghost" size="sm" className="text-gray-600">
                        <MessageCircle className="w-4 h-4 mr-1" />
                        Reply
                      </Button>
                    </div>
                  </div>

                  {review.response && (
                    <div className="mt-4 p-4 bg-orange-50 rounded-lg border-l-4 border-orange-600">
                      <div className="flex items-center gap-2 mb-2">
                        <Avatar className="h-8 w-8">
                          <div className="w-full h-full bg-orange-600 flex items-center justify-center text-white font-bold text-sm">
                            T
                          </div>
                        </Avatar>
                        <div>
                          <p className="font-semibold text-sm">{review.response.author}</p>
                          <p className="text-xs text-gray-600">{new Date(review.response.date).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <p className="text-gray-700">{review.response.content}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-orange-600 to-orange-700 py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Ready for Your Own Adventure?</h2>
            <p className="text-xl text-orange-100 mb-8 max-w-2xl mx-auto">
              Join thousands of satisfied travelers and create your own unforgettable safari experience
            </p>
            <div className="flex flex-col md:flex-row gap-4 justify-center">
              <Button className="bg-white text-orange-600 hover:bg-gray-100 font-semibold px-8 py-3">
                Book Your Safari
              </Button>
              <Button variant="outline" className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-3">
                Contact Us
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Reviews;
