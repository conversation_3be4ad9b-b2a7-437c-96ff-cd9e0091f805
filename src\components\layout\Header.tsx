
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Menu, X, User, Heart, Camera, LogOut, Settings } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AdvancedSearch from '@/components/features/AdvancedSearch';
import { useWishlist } from '@/contexts/WishlistContext';
import { useAuth } from '@/contexts/AuthContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const { wishlist } = useWishlist();
  const { currentUser, userProfile, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleAuthNavigation = (path: string) => {
    setIsAuthModalOpen(false);
    navigate(path);
  };

  const navItems = [
    { label: 'Tours', href: '/tours' },
    { label: 'Gallery', href: '/gallery' },
    { label: 'About', href: '/about' },
    { label: 'Contact', href: '/contact' },
  ];

  return (
    <header className="fixed top-0 w-full bg-gradient-to-r from-savannah-50 via-sunset-50 to-acacia-50 backdrop-blur-md shadow-xl z-50 border-b-2 border-savannah-200 bg-savannah-texture">
      {/* Subtle animated grass decoration */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-savannah-300 via-sunset-300 to-savannah-400 animate-horizon-glow"></div>

      <div className="container mx-auto px-4 md:px-6">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Enhanced Logo with Wildlife Theme */}
          <Link to="/" className="flex items-center space-x-2 md:space-x-3 group">
            <div className="relative bg-gradient-to-br from-wildlife-lion via-sunset-500 to-wildlife-cheetah text-white p-2 md:p-3 rounded-xl group-hover:scale-110 transition-all duration-300 shadow-xl animate-wildlife-emerge">
              {/* Subtle paw print pattern overlay */}
              <div className="absolute inset-0 bg-wildlife-pattern rounded-xl opacity-20"></div>
              <Camera className="h-4 w-4 md:h-6 md:w-6 relative z-10 animate-sun-rise" />
            </div>
            <div className="relative">
              <span className="font-bold text-xl md:text-2xl text-wildlife-gold font-safari animate-fade-in">SafariSole</span>
              <div className="text-xs md:text-sm text-savannah-700 font-medium hidden sm:block font-adventure">
                🌅 Wild Adventures Await
              </div>
              {/* Decorative accent */}
              <div className="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-sunset-400 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </Link>

          {/* Enhanced Desktop Navigation with Wildlife Theme */}
          <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8">
            {navItems.map((item, index) => (
              <Link
                key={item.href}
                to={item.href}
                className="relative text-savannah-800 hover:text-sunset-600 font-semibold transition-all duration-300 py-2 group text-sm xl:text-base font-adventure"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <span className="relative z-10 px-3 py-1 rounded-lg hover:bg-savannah-100/50 transition-all duration-300">
                  {item.label}
                </span>
                {/* Animated underline with savannah gradient */}
                <span className="absolute bottom-0 left-0 w-0 h-1 bg-gradient-to-r from-wildlife-lion via-sunset-500 to-wildlife-cheetah transition-all duration-300 group-hover:w-full rounded-full"></span>
                {/* Subtle glow effect on hover */}
                <span className="absolute inset-0 bg-gradient-to-r from-savannah-200/0 via-sunset-200/20 to-savannah-200/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-grass-sway"></span>
              </Link>
            ))}
          </nav>

          {/* Enhanced Search and Actions */}
          <div className="hidden lg:flex items-center space-x-3 xl:space-x-6">
            <div className="hidden xl:block">
              <AdvancedSearch />
            </div>

            {/* Enhanced Wishlist with Wildlife Theme */}
            {currentUser && (
              <Link to="/dashboard">
                <Button variant="ghost" size="sm" className="relative hover:bg-savannah-100 text-savannah-800 hover:text-sunset-600 p-2 rounded-xl transition-all duration-300 hover:scale-110 group">
                  <Heart className="h-4 w-4 md:h-5 md:w-5 group-hover:animate-animal-walk" />
                  {wishlist.length > 0 && (
                    <span className="absolute -top-1 -right-1 bg-gradient-to-r from-sunset-500 to-wildlife-cheetah text-white rounded-full text-xs w-4 h-4 md:w-5 md:h-5 flex items-center justify-center shadow-lg animate-wildlife-emerge">
                      {wishlist.length}
                    </span>
                  )}
                  {/* Subtle pulse effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-savannah-200/0 to-sunset-200/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Button>
              </Link>
            )}

            {/* User Authentication */}
            {currentUser ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="hover:bg-orange-100 text-amber-800 hover:text-orange-600 text-xs md:text-sm px-2 md:px-3">
                    <User className="h-4 w-4 mr-1 md:mr-2" />
                    <span className="hidden xl:inline">{userProfile?.displayName || currentUser.email}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-gradient-to-br from-amber-50 to-orange-50 border-orange-200">
                  <DropdownMenuItem asChild>
                    <Link to="/dashboard" className="flex items-center text-amber-800 hover:text-orange-600">
                      <User className="h-4 w-4 mr-2" />
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  {userProfile?.role === 'admin' && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin" className="flex items-center text-amber-800 hover:text-orange-600">
                        <Settings className="h-4 w-4 mr-2" />
                        Admin Panel
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={handleLogout} className="flex items-center text-amber-800 hover:text-orange-600">
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Dialog open={isAuthModalOpen} onOpenChange={setIsAuthModalOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-full px-3 md:px-6 text-xs md:text-sm">
                    <User className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                    <span className="hidden md:inline">Account</span>
                  </Button>
                </DialogTrigger>
                <DialogContent className="bg-gradient-to-br from-amber-50 to-orange-50 border-orange-200">
                  <DialogHeader>
                    <DialogTitle className="text-amber-800 text-center text-lg md:text-xl">Join SafariSole</DialogTitle>
                  </DialogHeader>
                  <div className="flex flex-col space-y-4 p-4 md:p-6">
                    <Button 
                      onClick={() => handleAuthNavigation('/login')}
                      variant="outline"
                      className="bg-white border-amber-300 text-amber-800 hover:bg-amber-50 hover:border-amber-400 transition-all duration-300"
                    >
                      Sign In
                    </Button>
                    <Button 
                      onClick={() => handleAuthNavigation('/register')}
                      className="bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg transition-all duration-300"
                    >
                      Create Account
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            )}

            <Link to="/tour-builder">
              <Button className="btn-wildlife relative overflow-hidden group">
                {/* Animated background pattern */}
                <div className="absolute inset-0 bg-wildlife-pattern opacity-10 group-hover:opacity-20 transition-opacity duration-300"></div>

                {/* Button content */}
                <span className="relative z-10 flex items-center space-x-2">
                  <span className="hidden md:inline font-bold">🦁 Plan Safari</span>
                  <span className="md:hidden font-bold">🦁 Plan</span>
                </span>

                {/* Subtle shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
              </Button>
            </Link>
          </div>

          {/* Enhanced Mobile menu button */}
          <button
            className="lg:hidden p-2 rounded-xl hover:bg-savannah-100 transition-all duration-300 text-savannah-800 hover:text-sunset-600 hover:scale-110 group"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <div className="relative">
              {isMenuOpen ? (
                <X className="h-5 w-5 md:h-6 md:w-6 group-hover:rotate-90 transition-transform duration-300" />
              ) : (
                <Menu className="h-5 w-5 md:h-6 md:w-6 group-hover:animate-grass-sway" />
              )}
              {/* Subtle glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-savannah-200/0 to-sunset-200/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
            </div>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-orange-200 bg-gradient-to-br from-amber-50 to-orange-50 backdrop-blur-md py-4 md:py-6 rounded-b-xl">
            <nav className="flex flex-col space-y-3 md:space-y-4">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  to={item.href}
                  className="text-amber-800 hover:text-orange-600 font-medium transition-colors px-4 py-2 rounded-lg hover:bg-orange-100 text-sm md:text-base"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <div className="px-4 pt-3 md:pt-4 border-t border-orange-200 space-y-3 md:space-y-4">
                <AdvancedSearch />
                <div className="flex items-center justify-between">
                  {currentUser ? (
                    <div className="flex items-center space-x-2 w-full">
                      <Link to="/dashboard" className="flex-1">
                        <Button variant="ghost" size="sm" className="w-full hover:bg-orange-100 text-amber-800 text-sm">
                          <User className="h-4 w-4 mr-2" />
                          Dashboard
                        </Button>
                      </Link>
                      <Button variant="ghost" size="sm" onClick={handleLogout} className="text-amber-800 hover:bg-orange-100">
                        <LogOut className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 w-full">
                      <Link to="/login" className="flex-1">
                        <Button variant="outline" size="sm" className="w-full border-amber-300 text-amber-800 hover:bg-amber-50 text-sm">Sign In</Button>
                      </Link>
                      <Link to="/register" className="flex-1">
                        <Button size="sm" className="w-full bg-gradient-to-r from-amber-600 to-orange-600 text-white text-sm">Sign Up</Button>
                      </Link>
                    </div>
                  )}
                </div>
                <Link to="/tour-builder" className="block">
                  <Button className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white text-sm">
                    Plan Safari
                  </Button>
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
