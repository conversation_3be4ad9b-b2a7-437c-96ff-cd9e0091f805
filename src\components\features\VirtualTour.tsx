
import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, RotateCcw, Volume2, Info, Navigation, Maximize } from 'lucide-react';

interface Hotspot {
  id: string;
  x: number;
  y: number;
  title: string;
  description: string;
  icon: string;
}

interface VirtualTourProps {
  destination: string;
  images: string[];
}

const VirtualTour: React.FC<VirtualTourProps> = ({ destination, images }) => {
  const [currentView, setCurrentView] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showHotspots, setShowHotspots] = useState(true);
  const [selectedHotspot, setSelectedHotspot] = useState<Hotspot | null>(null);
  const [rotation, setRotation] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Sample hotspots for each view
  const hotspots: { [key: number]: Hotspot[] } = {
    0: [
      { id: '1', x: 30, y: 40, title: 'Acacia Tree', description: 'Ancient acacia tree, home to various bird species', icon: '🌳' },
      { id: '2', x: 70, y: 60, title: 'Wildlife Viewing Area', description: 'Prime spot for game viewing', icon: '🦁' },
      { id: '3', x: 50, y: 20, title: 'Savanna Plains', description: 'Endless grasslands stretching to the horizon', icon: '🌾' }
    ],
    1: [
      { id: '4', x: 40, y: 50, title: 'Waterhole', description: 'Animals gather here during dry season', icon: '💧' },
      { id: '5', x: 80, y: 30, title: 'Rocky Outcrop', description: 'Perfect vantage point for photography', icon: '📸' }
    ],
    2: [
      { id: '6', x: 60, y: 45, title: 'Elephant Path', description: 'Traditional elephant migration route', icon: '🐘' },
      { id: '7', x: 25, y: 65, title: 'Baobab Tree', description: 'Ancient baobab tree, over 1000 years old', icon: '🌳' }
    ],
    3: [
      { id: '8', x: 45, y: 35, title: 'Kopje Formation', description: 'Ancient granite rock formation', icon: '🗻' },
      { id: '9', x: 75, y: 55, title: 'Predator Territory', description: 'Area frequently visited by big cats', icon: '🐆' }
    ]
  };

  const viewNames = [
    'Morning Safari Drive',
    'Watering Hole Vista',
    'Acacia Woodland',
    'Savanna Sunset'
  ];

  useEffect(() => {
    if (isPlaying) {
      intervalRef.current = setInterval(() => {
        setCurrentView(prev => (prev + 1) % images.length);
      }, 4000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isPlaying, images.length]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleFullscreen = () => {
    if (!isFullscreen && containerRef.current) {
      containerRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else if (document.fullscreenElement) {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleHotspotClick = (hotspot: Hotspot) => {
    setSelectedHotspot(hotspot);
  };

  const rotate360 = () => {
    setRotation(prev => prev + 90);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>360° Virtual Tour - {destination}</span>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowHotspots(!showHotspots)}
              className={showHotspots ? 'bg-blue-50 text-blue-600' : ''}
            >
              <Info className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={() => setSoundEnabled(!soundEnabled)}>
              <Volume2 className={`h-4 w-4 ${soundEnabled ? 'text-green-600' : 'text-gray-400'}`} />
            </Button>
            <Button variant="outline" size="sm" onClick={rotate360}>
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={() => setCurrentView(0)}>
              <Navigation className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={handlePlayPause}>
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button variant="outline" size="sm" onClick={handleFullscreen}>
              <Maximize className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div 
          ref={containerRef}
          className="relative aspect-video rounded-lg overflow-hidden bg-black group"
        >
          {/* Main 360° Image */}
          <div 
            className="relative w-full h-full transition-all duration-500 cursor-grab active:cursor-grabbing"
            style={{ transform: `rotate(${rotation}deg)` }}
          >
            <img
              src={`https://images.unsplash.com/${images[currentView]}?auto=format&fit=crop&w=1200&h=675`}
              alt={`${destination} view ${currentView + 1}`}
              className="w-full h-full object-cover transition-all duration-1000"
              draggable={false}
            />

            {/* Interactive Hotspots */}
            {showHotspots && hotspots[currentView] && (
              <>
                {hotspots[currentView].map((hotspot) => (
                  <div
                    key={hotspot.id}
                    className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group/hotspot"
                    style={{ left: `${hotspot.x}%`, top: `${hotspot.y}%` }}
                    onClick={() => handleHotspotClick(hotspot)}
                  >
                    {/* Pulsing Ring */}
                    <div className="absolute inset-0 w-8 h-8 bg-orange-500 rounded-full animate-ping opacity-30"></div>
                    
                    {/* Hotspot Icon */}
                    <div className="relative w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center text-white shadow-lg border-2 border-white hover:scale-110 transition-transform">
                      <span className="text-sm">{hotspot.icon}</span>
                    </div>

                    {/* Tooltip */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover/hotspot:opacity-100 transition-opacity pointer-events-none">
                      <div className="bg-black/80 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                        {hotspot.title}
                      </div>
                      <div className="w-2 h-2 bg-black/80 transform rotate-45 mx-auto -mt-1"></div>
                    </div>
                  </div>
                ))}
              </>
            )}

            {/* 360° Navigation Overlay */}
            <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="absolute top-1/2 left-4 transform -translate-y-1/2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-black/50 border-white/30 text-white hover:bg-black/70"
                  onClick={() => setCurrentView((prev) => (prev - 1 + images.length) % images.length)}
                >
                  ←
                </Button>
              </div>
              <div className="absolute top-1/2 right-4 transform -translate-y-1/2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-black/50 border-white/30 text-white hover:bg-black/70"
                  onClick={() => setCurrentView((prev) => (prev + 1) % images.length)}
                >
                  →
                </Button>
              </div>
            </div>
          </div>

          {/* Current View Badge */}
          <div className="absolute top-4 left-4">
            <Badge className="bg-black/70 text-white border-white/30">
              {viewNames[currentView] || `View ${currentView + 1}`}
            </Badge>
          </div>

          {/* View Indicators */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="flex justify-center space-x-2">
              {images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentView(index)}
                  className={`w-3 h-3 rounded-full transition-all ${
                    currentView === index 
                      ? 'bg-orange-500 scale-125' 
                      : 'bg-white/50 hover:bg-white/70'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Sound Indicator */}
          {soundEnabled && (
            <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm flex items-center">
              <Volume2 className="h-4 w-4 mr-1" />
              🎵 Ambient Sounds
            </div>
          )}

          {/* Auto-play Indicator */}
          {isPlaying && (
            <div className="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm flex items-center">
              <Play className="h-4 w-4 mr-1" />
              Auto Tour
            </div>
          )}
        </div>

        {/* Hotspot Information Panel */}
        {selectedHotspot && (
          <div className="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-orange-800 flex items-center">
                  <span className="text-lg mr-2">{selectedHotspot.icon}</span>
                  {selectedHotspot.title}
                </h4>
                <p className="text-orange-700 mt-1">{selectedHotspot.description}</p>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setSelectedHotspot(null)}
                className="text-orange-600 hover:text-orange-800"
              >
                ×
              </Button>
            </div>
          </div>
        )}

        {/* Tour Instructions */}
        <div className="mt-4 text-sm text-gray-600 space-y-2">
          <p className="flex items-center">
            <Info className="h-4 w-4 mr-2 text-blue-500" />
            Click and drag to explore the 360° environment. Use hotspots to learn about wildlife and landmarks.
          </p>
          <div className="flex flex-wrap gap-2 mt-2">
            <Badge variant="outline" className="text-xs">
              <Play className="h-3 w-3 mr-1" />
              Auto Tour
            </Badge>
            <Badge variant="outline" className="text-xs">
              <Volume2 className="h-3 w-3 mr-1" />
              Ambient Audio
            </Badge>
            <Badge variant="outline" className="text-xs">
              <Maximize className="h-3 w-3 mr-1" />
              Fullscreen
            </Badge>
            <Badge variant="outline" className="text-xs">
              {hotspots[currentView]?.length || 0} Interactive Points
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VirtualTour;
