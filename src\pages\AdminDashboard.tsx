
import React, { useState, useEffect } from 'react';
import { Timestamp } from 'firebase/firestore';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Users, 
  MapPin, 
  Calendar, 
  DollarSign, 
  Star, 
  Edit, 
  Trash2, 
  Plus,
  Eye,
  BookOpen,
  Settings,
  Activity,
  Building
} from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Tour, Booking, Review, Destination, Activity as ActivityType, Accommodation } from '@/types/firebase';

const AdminDashboard = () => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [activities, setActivities] = useState<ActivityType[]>([]);
  const [accommodations, setAccommodations] = useState<Accommodation[]>([]);
  
  const [stats, setStats] = useState({
    totalTours: 0,
    totalBookings: 0,
    totalRevenue: 0,
    averageRating: 0
  });

  const [newTour, setNewTour] = useState({
    title: '',
    description: '',
    price: 0,
    duration: '',
    location: '',
    destinations: [] as string[],
    activities: [] as string[],
    accommodations: [] as string[],
    maxGroupSize: 12,
    minGroupSize: 2,
    difficulty: 'moderate' as const,
    includes: [] as string[],
    excludes: [] as string[],
    images: [''],
    featured: false,
    status: 'active' as const,
    rating: 0,
    reviewCount: 0,
    tourType: 'standard' as const,
    seasonality: {
      greenSeason: true,
      drySeason: true,
      bestMonths: ['January', 'February', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
    },
    itinerary: [],
    fitnessRequirements: {
      level: 'moderate' as const,
      description: 'Moderate fitness required',
      walkingDistance: '2-5km daily',
      terrain: 'Mixed terrain',
      ageRestrictions: 'Suitable for ages 12+',
      medicalConditions: []
    },
    equipment: {
      provided: [],
      recommended: [],
      required: []
    },
    groupOptions: [],
    specialFeatures: [],
    difficultyDetails: 'Suitable for most fitness levels'
  });

  const [editingTour, setEditingTour] = useState<Tour | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Fetch all data
      const [toursData, bookingsData, reviewsData, destinationsData, activitiesData, accommodationsData] = 
        await Promise.all([
          FirebaseService.getTours(),
          FirebaseService.getBookings(),
          FirebaseService.getReviews(),
          FirebaseService.getDestinations(),
          FirebaseService.getActivities(),
          FirebaseService.getAccommodations()
        ]);

      setTours(toursData);
      setBookings(bookingsData);
      setReviews(reviewsData);
      setDestinations(destinationsData);
      setActivities(activitiesData);
      setAccommodations(accommodationsData);

      // Calculate stats
      const totalRevenue = bookingsData
        .filter(b => b.status === 'confirmed')
        .reduce((sum, booking) => sum + booking.totalPrice, 0);
      
      const averageRating = reviewsData.length > 0 
        ? reviewsData.reduce((sum, review) => sum + review.rating, 0) / reviewsData.length 
        : 0;

      setStats({
        totalTours: toursData.length,
        totalBookings: bookingsData.filter(b => b.status === 'confirmed').length,
        totalRevenue,
        averageRating
      });
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTour = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await FirebaseService.createTour(newTour);
      setNewTour({
        title: '',
        description: '',
        price: 0,
        duration: '',
        location: '',
        destinations: [],
        activities: [],
        accommodations: [],
        maxGroupSize: 12,
        minGroupSize: 2,
        difficulty: 'moderate',
        includes: [],
        excludes: [],
        images: [''],
        featured: false,
        status: 'active',
        rating: 0,
        reviewCount: 0,
        tourType: 'standard',
        seasonality: {
          greenSeason: true,
          drySeason: true,
          bestMonths: ['January', 'February', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
        },
        itinerary: [],
        fitnessRequirements: {
          level: 'moderate',
          description: 'Moderate fitness required',
          walkingDistance: '2-5km daily',
          terrain: 'Mixed terrain',
          ageRestrictions: 'Suitable for ages 12+',
          medicalConditions: []
        },
        equipment: {
          provided: [],
          recommended: [],
          required: []
        },
        groupOptions: [],
        specialFeatures: [],
        difficultyDetails: 'Suitable for most fitness levels'
      });
      fetchData();
    } catch (error) {
      console.error('Error creating tour:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateTour = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingTour) return;
    
    setLoading(true);
    try {
      const { id, createdAt, updatedAt, ...updateData } = editingTour;
      await FirebaseService.updateTour(id, updateData);
      setEditingTour(null);
      fetchData();
    } catch (error) {
      console.error('Error updating tour:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTour = async (tourId: string) => {
    if (window.confirm('Are you sure you want to delete this tour?')) {
      setLoading(true);
      try {
        await FirebaseService.deleteTour(tourId);
        fetchData();
      } catch (error) {
        console.error('Error deleting tour:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleUpdateBookingStatus = async (bookingId: string, status: Booking['status']) => {
    setLoading(true);
    try {
      await FirebaseService.updateBooking(bookingId, { status });
      fetchData();
    } catch (error) {
      console.error('Error updating booking:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateField: any) => {
    if (!dateField) return 'Recent';
    
    if (dateField instanceof Timestamp) {
      return dateField.toDate().toLocaleDateString();
    }
    
    if (dateField.toDate && typeof dateField.toDate === 'function') {
      return dateField.toDate().toLocaleDateString();
    }
    
    if (dateField instanceof Date) {
      return dateField.toLocaleDateString();
    }
    
    if (typeof dateField === 'string') {
      return new Date(dateField).toLocaleDateString();
    }
    
    return 'Recent';
  };

  const addIncludeItem = (value: string) => {
    if (editingTour) {
      setEditingTour({
        ...editingTour,
        includes: [...editingTour.includes, value]
      });
    } else {
      setNewTour({
        ...newTour,
        includes: [...newTour.includes, value]
      });
    }
  };

  const removeIncludeItem = (index: number) => {
    if (editingTour) {
      setEditingTour({
        ...editingTour,
        includes: editingTour.includes.filter((_, i) => i !== index)
      });
    } else {
      setNewTour({
        ...newTour,
        includes: newTour.includes.filter((_, i) => i !== index)
      });
    }
  };

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl font-bold mb-4">Admin Dashboard</h1>
            <p className="text-xl">Manage your safari business</p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Tours</p>
                    <p className="text-2xl font-bold">{stats.totalTours}</p>
                  </div>
                  <MapPin className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Confirmed Bookings</p>
                    <p className="text-2xl font-bold">{stats.totalBookings}</p>
                  </div>
                  <Calendar className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold">${stats.totalRevenue.toLocaleString()}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Average Rating</p>
                    <p className="text-2xl font-bold">{stats.averageRating.toFixed(1)}</p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="tours" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="tours">Tours</TabsTrigger>
              <TabsTrigger value="bookings">Bookings</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
              <TabsTrigger value="destinations">Destinations</TabsTrigger>
              <TabsTrigger value="activities">Activities</TabsTrigger>
              <TabsTrigger value="accommodations">Accommodations</TabsTrigger>
            </TabsList>

            <TabsContent value="tours" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Tour Management</h2>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Tour
                </Button>
              </div>

              {/* Create/Edit Tour Form */}
              <Card>
                <CardHeader>
                  <CardTitle>{editingTour ? 'Edit Tour' : 'Create New Tour'}</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={editingTour ? handleUpdateTour : handleCreateTour} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="title">Tour Title</Label>
                        <Input
                          id="title"
                          value={editingTour ? editingTour.title : newTour.title}
                          onChange={(e) => editingTour 
                            ? setEditingTour({...editingTour, title: e.target.value})
                            : setNewTour({...newTour, title: e.target.value})
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={editingTour ? editingTour.location : newTour.location}
                          onChange={(e) => editingTour
                            ? setEditingTour({...editingTour, location: e.target.value})
                            : setNewTour({...newTour, location: e.target.value})
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="price">Price ($)</Label>
                        <Input
                          id="price"
                          type="number"
                          value={editingTour ? editingTour.price : newTour.price}
                          onChange={(e) => editingTour
                            ? setEditingTour({...editingTour, price: Number(e.target.value)})
                            : setNewTour({...newTour, price: Number(e.target.value)})
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="duration">Duration</Label>
                        <Input
                          id="duration"
                          value={editingTour ? editingTour.duration : newTour.duration}
                          onChange={(e) => editingTour
                            ? setEditingTour({...editingTour, duration: e.target.value})
                            : setNewTour({...newTour, duration: e.target.value})
                          }
                          placeholder="e.g., 5 days"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="maxGroupSize">Max Group Size</Label>
                        <Input
                          id="maxGroupSize"
                          type="number"
                          value={editingTour ? editingTour.maxGroupSize : newTour.maxGroupSize}
                          onChange={(e) => editingTour
                            ? setEditingTour({...editingTour, maxGroupSize: Number(e.target.value)})
                            : setNewTour({...newTour, maxGroupSize: Number(e.target.value)})
                          }
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="difficulty">Difficulty</Label>
                        <Select
                          value={editingTour ? editingTour.difficulty : newTour.difficulty}
                          onValueChange={(value: any) => editingTour
                            ? setEditingTour({...editingTour, difficulty: value})
                            : setNewTour({...newTour, difficulty: value})
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="easy">Easy</SelectItem>
                            <SelectItem value="moderate">Moderate</SelectItem>
                            <SelectItem value="challenging">Challenging</SelectItem>
                            <SelectItem value="extreme">Extreme</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={editingTour ? editingTour.description : newTour.description}
                        onChange={(e) => editingTour
                          ? setEditingTour({...editingTour, description: e.target.value})
                          : setNewTour({...newTour, description: e.target.value})
                        }
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="image">Main Image URL</Label>
                      <Input
                        id="image"
                        value={editingTour ? editingTour.images[0] : newTour.images[0]}
                        onChange={(e) => {
                          const newImages = editingTour ? [...editingTour.images] : [...newTour.images];
                          newImages[0] = e.target.value;
                          editingTour
                            ? setEditingTour({...editingTour, images: newImages})
                            : setNewTour({...newTour, images: newImages});
                        }}
                        placeholder="https://images.unsplash.com/..."
                        required
                      />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="featured"
                        checked={editingTour ? editingTour.featured : newTour.featured}
                        onChange={(e) => editingTour
                          ? setEditingTour({...editingTour, featured: e.target.checked})
                          : setNewTour({...newTour, featured: e.target.checked})
                        }
                      />
                      <Label htmlFor="featured">Featured Tour</Label>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button type="submit" disabled={loading}>
                        {editingTour ? 'Update Tour' : 'Create Tour'}
                      </Button>
                      {editingTour && (
                        <Button type="button" variant="outline" onClick={() => setEditingTour(null)}>
                          Cancel
                        </Button>
                      )}
                    </div>
                  </form>
                </CardContent>
              </Card>

              {/* Tours List */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {tours.map((tour) => (
                  <Card key={tour.id}>
                    <div className="relative">
                      <img
                        src={tour.images[0]}
                        alt={tour.title}
                        className="w-full h-48 object-cover rounded-t-lg"
                      />
                      {tour.featured && (
                        <Badge className="absolute top-2 right-2 bg-yellow-600">Featured</Badge>
                      )}
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold mb-2">{tour.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{tour.location}</p>
                      <p className="text-lg font-bold text-orange-600 mb-3">${tour.price}</p>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => setEditingTour(tour)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={() => handleDeleteTour(tour.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="bookings" className="space-y-6">
              <h2 className="text-2xl font-bold">Booking Management</h2>
              <div className="space-y-4">
                {bookings.map((booking) => (
                  <Card key={booking.id}>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold text-lg">{booking.tourTitle}</h3>
                          <p className="text-gray-600">{booking.userName} ({booking.userEmail})</p>
                          <p className="text-sm text-gray-500">
                            {booking.startDate} • {booking.guests} guests • ${booking.totalPrice}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant={
                              booking.status === 'confirmed' ? 'default' : 
                              booking.status === 'pending' ? 'secondary' : 'destructive'
                            }
                          >
                            {booking.status}
                          </Badge>
                          {booking.status === 'pending' && (
                            <>
                              <Button 
                                size="sm" 
                                onClick={() => handleUpdateBookingStatus(booking.id, 'confirmed')}
                              >
                                Confirm
                              </Button>
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => handleUpdateBookingStatus(booking.id, 'cancelled')}
                              >
                                Cancel
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="reviews" className="space-y-6">
              <h2 className="text-2xl font-bold">Review Management</h2>
              <div className="space-y-4">
                {reviews.map((review) => (
                  <Card key={review.id}>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold">{review.userName}</h4>
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < review.rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            {review.verified && (
                              <Badge variant="secondary">Verified</Badge>
                            )}
                          </div>
                          <p className="text-gray-700">{review.comment}</p>
                          <p className="text-sm text-gray-500 mt-2">
                            {formatDate(review.createdAt)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="destinations" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Destination Management</h2>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Destination
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {destinations.map((destination) => (
                  <Card key={destination.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <MapPin className="h-5 w-5 text-orange-600" />
                        <h3 className="font-semibold">{destination.name}</h3>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{destination.country}</p>
                      <p className="text-sm text-gray-500">{destination.wildlife.length} species</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="activities" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Activity Management</h2>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Activity
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {activities.map((activity) => (
                  <Card key={activity.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Activity className="h-5 w-5 text-green-600" />
                        <h3 className="font-semibold">{activity.name}</h3>
                      </div>
                      <Badge variant="outline" className="mb-2">{activity.category}</Badge>
                      <p className="text-sm text-gray-600">{activity.duration}</p>
                      <p className="text-sm font-semibold text-green-600">${activity.price}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="accommodations" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold">Accommodation Management</h2>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Accommodation
                </Button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {accommodations.map((accommodation) => (
                  <Card key={accommodation.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Building className="h-5 w-5 text-blue-600" />
                        <h3 className="font-semibold">{accommodation.name}</h3>
                      </div>
                      <Badge variant="outline" className="mb-2">{accommodation.category}</Badge>
                      <p className="text-sm text-gray-600">{accommodation.location}</p>
                      <p className="text-sm font-semibold text-blue-600">${accommodation.pricePerNight}/night</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AdminDashboard;
