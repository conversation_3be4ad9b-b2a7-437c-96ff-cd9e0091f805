import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  Timestamp,
  DocumentSnapshot,
  QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { 
  Tour, 
  Booking, 
  Review, 
  UserProfile, 
  Destination, 
  Activity, 
  Accommodation,
  WildlifeSighting,
  ChatMessage,
  WeatherData,
  VirtualTour,
  Notification,
  PaymentTransaction,
  ContentPage,
  BlogPost,
  Wishlist,
  TourPackage,
  Guide,
  TravelGuide,
  PackingList,
  SearchFilters,
  TourAvailability
} from '@/types/firebase';

// Generic CRUD operations
export class FirebaseService {
  // Enhanced Tours with new features
  static async getTours(constraints: QueryConstraint[] = []) {
    const q = query(collection(db, 'tours'), orderBy('createdAt', 'desc'), ...constraints);
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Tour[];
  }

  static async getToursWithFilters(filters: SearchFilters) {
    let constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')];
    
    // Add filter constraints
    if (filters.destination && filters.destination.length > 0) {
      constraints.push(where('destinations', 'array-contains-any', filters.destination));
    }
    
    if (filters.tourType && filters.tourType.length > 0) {
      constraints.push(where('tourType', 'in', filters.tourType));
    }
    
    if (filters.difficulty && filters.difficulty.length > 0) {
      constraints.push(where('difficulty', 'in', filters.difficulty));
    }
    
    if (filters.photography) {
      constraints.push(where('tourType', '==', 'photography'));
    }
    
    if (filters.birding) {
      constraints.push(where('tourType', '==', 'birding'));
    }
    
    if (filters.conservation) {
      constraints.push(where('tourType', '==', 'conservation'));
    }
    
    if (filters.cultural) {
      constraints.push(where('tourType', '==', 'cultural'));
    }

    const q = query(collection(db, 'tours'), ...constraints);
    const snapshot = await getDocs(q);
    let tours = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Tour[];
    
    // Apply client-side filters for complex conditions
    if (filters.price) {
      tours = tours.filter(tour => 
        tour.price >= (filters.price!.min || 0) && 
        tour.price <= (filters.price!.max || Infinity)
      );
    }
    
    if (filters.duration) {
      tours = tours.filter(tour => {
        const days = parseInt(tour.duration.split(' ')[0]);
        return days >= (filters.duration!.min || 0) && 
               days <= (filters.duration!.max || Infinity);
      });
    }
    
    if (filters.groupSize) {
      tours = tours.filter(tour => tour.maxGroupSize >= filters.groupSize!);
    }
    
    if (filters.wildlife && filters.wildlife.length > 0) {
      // This would need to be implemented based on tour descriptions or linked data
      // For now, we'll keep all tours
    }
    
    return tours;
  }

  static async getFeaturedTours(limit: number = 6) {
    const q = query(
      collection(db, 'tours'), 
      where('featured', '==', true),
      where('status', '==', 'active'),
      orderBy('rating', 'desc'),
      orderBy('createdAt', 'desc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.slice(0, limit).map(doc => ({ id: doc.id, ...doc.data() })) as Tour[];
  }

  static async getTour(id: string): Promise<Tour | null> {
    const docRef = doc(db, 'tours', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as Tour : null;
  }

  static async createTour(tour: Omit<Tour, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'tours'), {
      ...tour,
      createdAt: now,
      updatedAt: now
    });
  }

  static async updateTour(id: string, updates: Partial<Tour>) {
    const docRef = doc(db, 'tours', id);
    return await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  }

  static async deleteTour(id: string) {
    return await deleteDoc(doc(db, 'tours', id));
  }

  // Tour Packages
  static async getTourPackages() {
    const q = query(collection(db, 'tourPackages'), orderBy('featured', 'desc'), orderBy('createdAt', 'desc'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as TourPackage[];
  }

  static async getTourPackage(id: string): Promise<TourPackage | null> {
    const docRef = doc(db, 'tourPackages', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as TourPackage : null;
  }

  static async createTourPackage(tourPackage: Omit<TourPackage, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'tourPackages'), {
      ...tourPackage,
      createdAt: now,
      updatedAt: now
    });
  }

  // Tour Availability
  static async getTourAvailability(tourId: string, startDate: string, endDate: string): Promise<TourAvailability[]> {
    const q = query(
      collection(db, 'tourAvailability'),
      where('tourId', '==', tourId),
      where('date', '>=', startDate),
      where('date', '<=', endDate),
      orderBy('date')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ 
      tourId: doc.data().tourId || tourId,
      date: doc.data().date || '',
      available: doc.data().available || true,
      spotsRemaining: doc.data().spotsRemaining || 12,
      priceModifier: doc.data().priceModifier || 1,
      season: doc.data().season || 'medium',
      wildlifeActivity: doc.data().wildlifeActivity || 'good',
      weatherConditions: doc.data().weatherConditions || 'good'
    })) as TourAvailability[];
  }

  static async updateTourAvailability(tourId: string, date: string, updates: Partial<TourAvailability>) {
    const q = query(
      collection(db, 'tourAvailability'),
      where('tourId', '==', tourId),
      where('date', '==', date)
    );
    const snapshot = await getDocs(q);
    
    if (!snapshot.empty) {
      const docRef = doc(db, 'tourAvailability', snapshot.docs[0].id);
      return await updateDoc(docRef, updates);
    } else {
      return await addDoc(collection(db, 'tourAvailability'), {
        tourId,
        date,
        ...updates
      });
    }
  }

  // Enhanced Bookings
  static async getBookings(userId?: string) {
    const constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')];
    if (userId) {
      constraints.unshift(where('userId', '==', userId));
    }
    const q = query(collection(db, 'bookings'), ...constraints);
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Booking[];
  }

  static async getBooking(id: string): Promise<Booking | null> {
    const docRef = doc(db, 'bookings', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as Booking : null;
  }

  static async createBooking(booking: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'bookings'), {
      ...booking,
      createdAt: now,
      updatedAt: now
    });
  }

  static async updateBooking(id: string, updates: Partial<Booking>) {
    const docRef = doc(db, 'bookings', id);
    return await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  }

  // Enhanced Reviews
  static async getReviews(tourId?: string) {
    const constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')];
    if (tourId) {
      constraints.unshift(where('tourId', '==', tourId));
    }
    const q = query(collection(db, 'reviews'), ...constraints);
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Review[];
  }

  static async createReview(review: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'reviews'), {
      ...review,
      createdAt: now,
      updatedAt: now
    });
  }

  // Enhanced Destinations
  static async getDestinations() {
    const q = query(collection(db, 'destinations'), orderBy('name'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Destination[];
  }

  static async getDestination(id: string): Promise<Destination | null> {
    const docRef = doc(db, 'destinations', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as Destination : null;
  }

  static async createDestination(destination: Omit<Destination, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'destinations'), {
      ...destination,
      createdAt: now,
      updatedAt: now
    });
  }

  // Enhanced Activities
  static async getActivities() {
    const q = query(collection(db, 'activities'), orderBy('name'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Activity[];
  }

  static async getActivitiesByCategory(category: string) {
    const q = query(collection(db, 'activities'), where('category', '==', category), orderBy('name'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Activity[];
  }

  static async createActivity(activity: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'activities'), {
      ...activity,
      createdAt: now,
      updatedAt: now
    });
  }

  // Enhanced Accommodations
  static async getAccommodations() {
    const q = query(collection(db, 'accommodations'), orderBy('name'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Accommodation[];
  }

  static async getAccommodationsByCategory(category: string) {
    const q = query(collection(db, 'accommodations'), where('category', '==', category), orderBy('rating', 'desc'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Accommodation[];
  }

  static async createAccommodation(accommodation: Omit<Accommodation, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'accommodations'), {
      ...accommodation,
      createdAt: now,
      updatedAt: now
    });
  }

  // Guides Management
  static async getGuides() {
    const q = query(collection(db, 'guides'), orderBy('rating', 'desc'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Guide[];
  }

  static async getAvailableGuides(startDate: string, endDate: string) {
    // This would need more complex logic to check availability
    const q = query(collection(db, 'guides'), orderBy('rating', 'desc'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Guide[];
  }

  static async createGuide(guide: Omit<Guide, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'guides'), {
      ...guide,
      createdAt: now,
      updatedAt: now
    });
  }

  // Travel Guides and Content
  static async getTravelGuides(category?: string) {
    let constraints: QueryConstraint[] = [orderBy('featured', 'desc'), orderBy('createdAt', 'desc')];
    if (category) {
      constraints.unshift(where('category', '==', category));
    }
    const q = query(collection(db, 'travelGuides'), ...constraints);
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as TravelGuide[];
  }

  static async createTravelGuide(guide: Omit<TravelGuide, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'travelGuides'), {
      ...guide,
      createdAt: now,
      updatedAt: now
    });
  }

  // Packing Lists
  static async getPackingLists(tourType?: string) {
    let constraints: QueryConstraint[] = [orderBy('tourType')];
    if (tourType) {
      constraints.unshift(where('tourType', '==', tourType));
    }
    const q = query(collection(db, 'packingLists'), ...constraints);
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as PackingList[];
  }

  static async createPackingList(packingList: Omit<PackingList, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'packingLists'), {
      ...packingList,
      createdAt: now,
      updatedAt: now
    });
  }

  // Enhanced Wildlife Sightings
  static async getWildlifeSightings(destinationId?: string) {
    const constraints: QueryConstraint[] = [orderBy('date', 'desc'), limit(50)];
    if (destinationId) {
      constraints.unshift(where('destinationId', '==', destinationId));
    }
    const q = query(collection(db, 'wildlifeSightings'), ...constraints);
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as WildlifeSighting[];
  }

  static async createWildlifeSighting(sighting: Omit<WildlifeSighting, 'id' | 'createdAt'>) {
    return await addDoc(collection(db, 'wildlifeSightings'), {
      ...sighting,
      createdAt: Timestamp.now()
    });
  }

  // Enhanced Chat Messages
  static async getChatMessages(sessionId: string) {
    const q = query(
      collection(db, 'chatMessages'), 
      where('sessionId', '==', sessionId),
      orderBy('timestamp', 'asc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as ChatMessage[];
  }

  static async createChatMessage(message: Omit<ChatMessage, 'id'>) {
    return await addDoc(collection(db, 'chatMessages'), message);
  }

  // Enhanced Weather Data
  static async getWeatherData(destinationId: string, days: number = 7) {
    const q = query(
      collection(db, 'weatherData'),
      where('destinationId', '==', destinationId),
      orderBy('date', 'desc'),
      limit(days)
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as WeatherData[];
  }

  static async createWeatherData(weather: Omit<WeatherData, 'id' | 'createdAt'>) {
    return await addDoc(collection(db, 'weatherData'), {
      ...weather,
      createdAt: Timestamp.now()
    });
  }

  // Enhanced Virtual Tours
  static async getVirtualTours() {
    const q = query(collection(db, 'virtualTours'), orderBy('createdAt', 'desc'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as VirtualTour[];
  }

  static async getVirtualTour(id: string): Promise<VirtualTour | null> {
    const docRef = doc(db, 'virtualTours', id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { id: docSnap.id, ...docSnap.data() } as VirtualTour : null;
  }

  // Enhanced Notifications
  static async getUserNotifications(userId: string) {
    const q = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(50)
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Notification[];
  }

  static async createNotification(notification: Omit<Notification, 'id'>) {
    return await addDoc(collection(db, 'notifications'), notification);
  }

  static async markNotificationAsRead(id: string) {
    const docRef = doc(db, 'notifications', id);
    return await updateDoc(docRef, { read: true });
  }

  // Enhanced Payment Transactions
  static async getPaymentTransactions(bookingId: string) {
    const q = query(
      collection(db, 'paymentTransactions'),
      where('bookingId', '==', bookingId),
      orderBy('createdAt', 'desc')
    );
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as PaymentTransaction[];
  }

  static async createPaymentTransaction(transaction: Omit<PaymentTransaction, 'id' | 'createdAt' | 'updatedAt'>) {
    const now = Timestamp.now();
    return await addDoc(collection(db, 'paymentTransactions'), {
      ...transaction,
      createdAt: now,
      updatedAt: now
    });
  }

  // Enhanced Content Management
  static async getContentPages() {
    const q = query(collection(db, 'contentPages'), orderBy('title'));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as ContentPage[];
  }

  static async getBlogPosts(published: boolean = true) {
    const constraints: QueryConstraint[] = [orderBy('publishedAt', 'desc')];
    if (published) {
      constraints.unshift(where('published', '==', true));
    }
    const q = query(collection(db, 'blogPosts'), ...constraints);
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as BlogPost[];
  }

  // Enhanced Wishlist
  static async getUserWishlist(userId: string): Promise<Wishlist | null> {
    const q = query(collection(db, 'wishlists'), where('userId', '==', userId));
    const snapshot = await getDocs(q);
    if (snapshot.empty) return null;
    const doc = snapshot.docs[0];
    return { id: doc.id, ...doc.data() } as Wishlist;
  }

  static async updateWishlist(userId: string, tourIds: string[], destinationIds: string[], packageIds: string[] = []) {
    const wishlist = await this.getUserWishlist(userId);
    const now = Timestamp.now();
    
    if (wishlist) {
      const docRef = doc(db, 'wishlists', wishlist.id);
      return await updateDoc(docRef, {
        tourIds,
        destinationIds,
        packageIds,
        updatedAt: now
      });
    } else {
      return await addDoc(collection(db, 'wishlists'), {
        userId,
        tourIds,
        destinationIds,
        packageIds,
        createdAt: now,
        updatedAt: now
      });
    }
  }

  // User Profiles
  static async getUserProfile(uid: string): Promise<UserProfile | null> {
    const docRef = doc(db, 'users', uid);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? { uid: docSnap.id, ...docSnap.data() } as UserProfile : null;
  }

  static async updateUserProfile(uid: string, updates: Partial<UserProfile>) {
    const docRef = doc(db, 'users', uid);
    return await updateDoc(docRef, {
      ...updates,
      updatedAt: Timestamp.now()
    });
  }

  // Enhanced Analytics helpers
  static async getBookingStats() {
    const bookings = await this.getBookings();
    const confirmed = bookings.filter(b => b.status === 'confirmed');
    const totalRevenue = confirmed.reduce((sum, b) => sum + b.totalPrice, 0);
    
    return {
      totalBookings: bookings.length,
      confirmedBookings: confirmed.length,
      totalRevenue,
      averageBookingValue: confirmed.length > 0 ? totalRevenue / confirmed.length : 0,
      packageBookings: bookings.filter(b => b.packageBookings && b.packageBookings.length > 0).length
    };
  }

  static async getTourStats() {
    const tours = await this.getTours();
    const reviews = await this.getReviews();
    
    const avgRating = reviews.length > 0 
      ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length 
      : 0;

    const tourTypeStats = tours.reduce((acc, tour) => {
      acc[tour.tourType] = (acc[tour.tourType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalTours: tours.length,
      activeTours: tours.filter(t => t.status === 'active').length,
      featuredTours: tours.filter(t => t.featured).length,
      averageRating: avgRating,
      totalReviews: reviews.length,
      tourTypeBreakdown: tourTypeStats
    };
  }

  // Search and Filter Helpers
  static async searchTours(searchTerm: string, filters?: SearchFilters) {
    let tours = filters ? await this.getToursWithFilters(filters) : await this.getTours();
    
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      tours = tours.filter(tour => 
        tour.title.toLowerCase().includes(searchLower) ||
        tour.description.toLowerCase().includes(searchLower) ||
        tour.location.toLowerCase().includes(searchLower) ||
        tour.destinations.some(dest => dest.toLowerCase().includes(searchLower)) ||
        tour.activities.some(activity => activity.toLowerCase().includes(searchLower))
      );
    }
    
    return tours;
  }
}
