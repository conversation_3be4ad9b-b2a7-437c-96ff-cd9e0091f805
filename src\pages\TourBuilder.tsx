
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import TourBuilderSteps from '@/components/tour-builder/TourBuilderSteps';
import DestinationSelector from '@/components/tour-builder/DestinationSelector';
import AccommodationSelector from '@/components/tour-builder/AccommodationSelector';
import ActivitySelector from '@/components/tour-builder/ActivitySelector';
import ItineraryBuilder from '@/components/tour-builder/ItineraryBuilder';
import PricingCalculator from '@/components/tour-builder/PricingCalculator';
import GoogleMap from '@/components/maps/GoogleMap';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Users, MapPin, DollarSign, Clock, Star } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface TourBuilderData {
  destinations: string[];
  accommodation: string;
  activities: string[];
  duration: number;
  groupSize: number;
  startDate: Date | null;
  itinerary: Array<{
    day: number;
    destination: string;
    activities: string[];
    accommodation: string;
  }>;
  totalPrice: number;
  specialRequests: string;
  preferences: {
    transportation: string;
    mealPlan: string;
    guideLanguage: string;
    fitnessLevel: string;
  };
}

const TourBuilder = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [tourData, setTourData] = useState<TourBuilderData>({
    destinations: [],
    accommodation: '',
    activities: [],
    duration: 5,
    groupSize: 2,
    startDate: null,
    itinerary: [],
    totalPrice: 0,
    specialRequests: '',
    preferences: {
      transportation: 'safari-vehicle',
      mealPlan: 'full-board',
      guideLanguage: 'english',
      fitnessLevel: 'moderate'
    }
  });

  const steps = [
    { id: 1, title: 'Destinations', description: 'Choose your safari destinations' },
    { id: 2, title: 'Accommodation', description: 'Select accommodation level' },
    { id: 3, title: 'Activities', description: 'Pick your activities' },
    { id: 4, title: 'Itinerary', description: 'Build your day-by-day plan' },
    { id: 5, title: 'Preferences', description: 'Customize your experience' },
    { id: 6, title: 'Review', description: 'Review and book your tour' }
  ];

  const destinationCoordinates = {
    'serengeti': { id: 'serengeti', name: 'Serengeti National Park', lat: -2.153389, lng: 34.6857, description: 'Home to the Great Migration', image: 'photo-1472396961693-142e6e269027' },
    'ngorongoro': { id: 'ngorongoro', name: 'Ngorongoro Crater', lat: -3.2175, lng: 35.5, description: 'World\'s largest intact volcanic caldera', image: 'photo-1466721591366-2d5fba72006d' },
    'tarangire': { id: 'tarangire', name: 'Tarangire National Park', lat: -3.8333, lng: 35.85, description: 'Famous for elephant herds', image: 'photo-1493962853295-0fd70327578a' },
    'manyara': { id: 'manyara', name: 'Lake Manyara', lat: -3.3667, lng: 35.8167, description: 'Known for tree-climbing lions', image: 'photo-1485833077593-4278bba3f11f' }
  };

  const updateTourData = (field: keyof TourBuilderData, value: any) => {
    setTourData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getSelectedDestinationCoords = () => {
    return tourData.destinations.map(destId => destinationCoordinates[destId as keyof typeof destinationCoordinates]).filter(Boolean);
  };

  const handleBookNow = async () => {
    setIsSubmitting(true);
    try {
      // Create a custom tour object that matches the booking system requirements
      const customTour = {
        id: 'custom-' + Date.now(),
        title: `Custom ${tourData.duration}-Day Safari`,
        description: `Personalized safari experience visiting ${tourData.destinations.map(d => destinationCoordinates[d as keyof typeof destinationCoordinates]?.name).join(', ')}`,
        price: tourData.totalPrice,
        duration: `${tourData.duration} days`,
        location: tourData.destinations.length > 1 ? 'Multiple Destinations' : destinationCoordinates[tourData.destinations[0] as keyof typeof destinationCoordinates]?.name || 'Custom Tour',
        destinations: tourData.destinations,
        activities: tourData.activities,
        accommodations: [tourData.accommodation],
        maxGroupSize: 12,
        minGroupSize: 1,
        difficulty: tourData.preferences.fitnessLevel,
        tourType: 'custom',
        seasonality: ['year-round'],
        itinerary: tourData.itinerary.map(day => ({
          day: day.day,
          title: `Day ${day.day}: ${day.destination}`,
          description: `Explore ${day.destination} with activities: ${day.activities.join(', ')}`,
          location: day.destination,
          activities: day.activities,
          accommodation: day.accommodation,
          meals: ['breakfast', 'lunch', 'dinner'],
          highlights: day.activities
        })),
        inclusions: [
          'Professional safari guide',
          'Game drives',
          `${tourData.preferences.mealPlan} meal plan`,
          'Park entrance fees',
          'Transportation',
          'Accommodation'
        ],
        exclusions: [
          'International flights',
          'Travel insurance',
          'Personal expenses',
          'Tips and gratuities'
        ],
        images: tourData.destinations.map(destId => {
          const dest = destinationCoordinates[destId as keyof typeof destinationCoordinates];
          return `https://images.unsplash.com/${dest?.image}?auto=format&fit=crop&w=800&h=600`;
        }),
        featured: false,
        rating: 4.8,
        reviewCount: 0,
        lastBooking: new Date().toISOString(),
        popularity: 0,
        equipment: {
          required: [
            { name: 'Passport', description: 'Valid passport required', category: 'safety', optional: false },
            { name: 'Binoculars', description: 'For wildlife viewing', category: 'gear', optional: false }
          ],
          recommended: [
            { name: 'Camera', description: 'For capturing memories', category: 'photography', optional: true },
            { name: 'Sun hat', description: 'Protection from sun', category: 'clothing', optional: true }
          ]
        },
        physicalRequirements: 'Moderate fitness level required for walking and getting in/out of safari vehicles',
        difficultyDetails: `This ${tourData.duration}-day safari requires a ${tourData.preferences.fitnessLevel} fitness level.`,
        ageRestrictions: { min: 5, max: 85 },
        customData: {
          builderData: tourData,
          createdAt: new Date().toISOString()
        }
      };

      // Store the custom tour in sessionStorage for the booking page
      sessionStorage.setItem('customTour', JSON.stringify(customTour));
      
      toast({
        title: "Custom Tour Created!",
        description: "Proceeding to booking page...",
      });

      // Navigate to booking page with custom tour
      setTimeout(() => {
        navigate(`/book/custom-${Date.now()}`);
      }, 1000);

    } catch (error) {
      console.error('Error creating custom tour:', error);
      toast({
        title: "Error",
        description: "Failed to create custom tour. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-8">
            <DestinationSelector
              selectedDestinations={tourData.destinations}
              onDestinationsChange={(destinations) => updateTourData('destinations', destinations)}
              duration={tourData.duration}
              onDurationChange={(duration) => updateTourData('duration', duration)}
            />
            
            {tourData.destinations.length > 0 && (
              <div>
                <h3 className="text-xl font-semibold mb-4">Your Selected Route</h3>
                <GoogleMap 
                  destinations={getSelectedDestinationCoords()}
                  showRoutes={true}
                  height="400px"
                />
              </div>
            )}
          </div>
        );
      case 2:
        return (
          <AccommodationSelector
            selectedAccommodation={tourData.accommodation}
            onAccommodationChange={(accommodation) => updateTourData('accommodation', accommodation)}
            destinations={tourData.destinations}
          />
        );
      case 3:
        return (
          <ActivitySelector
            selectedActivities={tourData.activities}
            onActivitiesChange={(activities) => updateTourData('activities', activities)}
            destinations={tourData.destinations}
          />
        );
      case 4:
        return (
          <ItineraryBuilder
            destinations={tourData.destinations}
            activities={tourData.activities}
            duration={tourData.duration}
            itinerary={tourData.itinerary}
            onItineraryChange={(itinerary) => updateTourData('itinerary', itinerary)}
          />
        );
      case 5:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Customize Your Experience</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Transportation</label>
                  <select 
                    className="w-full p-2 border rounded-lg"
                    value={tourData.preferences.transportation}
                    onChange={(e) => updateTourData('preferences', {...tourData.preferences, transportation: e.target.value})}
                  >
                    <option value="safari-vehicle">Safari Vehicle</option>
                    <option value="luxury-vehicle">Luxury Vehicle</option>
                    <option value="helicopter">Helicopter</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Meal Plan</label>
                  <select 
                    className="w-full p-2 border rounded-lg"
                    value={tourData.preferences.mealPlan}
                    onChange={(e) => updateTourData('preferences', {...tourData.preferences, mealPlan: e.target.value})}
                  >
                    <option value="full-board">Full Board</option>
                    <option value="half-board">Half Board</option>
                    <option value="breakfast-only">Breakfast Only</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Guide Language</label>
                  <select 
                    className="w-full p-2 border rounded-lg"
                    value={tourData.preferences.guideLanguage}
                    onChange={(e) => updateTourData('preferences', {...tourData.preferences, guideLanguage: e.target.value})}
                  >
                    <option value="english">English</option>
                    <option value="swahili">Swahili</option>
                    <option value="french">French</option>
                    <option value="german">German</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Fitness Level</label>
                  <select 
                    className="w-full p-2 border rounded-lg"
                    value={tourData.preferences.fitnessLevel}
                    onChange={(e) => updateTourData('preferences', {...tourData.preferences, fitnessLevel: e.target.value})}
                  >
                    <option value="easy">Easy</option>
                    <option value="moderate">Moderate</option>
                    <option value="challenging">Challenging</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Special Requests</label>
                <textarea 
                  className="w-full p-2 border rounded-lg h-24"
                  placeholder="Any special requests or dietary requirements..."
                  value={tourData.specialRequests}
                  onChange={(e) => updateTourData('specialRequests', e.target.value)}
                />
              </div>
            </CardContent>
          </Card>
        );
      case 6:
        return (
          <div className="space-y-6">
            {/* Enhanced Tour Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="mr-2 h-5 w-5 text-orange-600" />
                  Your Custom Safari Tour Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <Calendar className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{tourData.duration}</div>
                    <div className="text-sm text-gray-600">Days</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{tourData.groupSize}</div>
                    <div className="text-sm text-gray-600">Travelers</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <MapPin className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">{tourData.destinations.length}</div>
                    <div className="text-sm text-gray-600">Destinations</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <DollarSign className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">${tourData.totalPrice}</div>
                    <div className="text-sm text-gray-600">Total Price</div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-orange-600" />
                      Destinations
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {tourData.destinations.map(destId => {
                        const dest = destinationCoordinates[destId as keyof typeof destinationCoordinates];
                        return dest ? (
                          <Badge key={destId} className="bg-orange-100 text-orange-800">
                            {dest.name}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Accommodation</h4>
                    <Badge className="bg-blue-100 text-blue-800 capitalize">
                      {tourData.accommodation || 'Not selected'}
                    </Badge>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Activities</h4>
                    <div className="flex flex-wrap gap-2">
                      {tourData.activities.map((activity, index) => (
                        <Badge key={index} variant="outline">
                          {activity}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Preferences</h4>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>Transport: {tourData.preferences.transportation}</p>
                      <p>Meals: {tourData.preferences.mealPlan}</p>
                      <p>Guide: {tourData.preferences.guideLanguage}</p>
                      <p>Fitness: {tourData.preferences.fitnessLevel}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Route Map */}
            {tourData.destinations.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Your Safari Route</CardTitle>
                </CardHeader>
                <CardContent>
                  <GoogleMap 
                    destinations={getSelectedDestinationCoords()}
                    showRoutes={true}
                    height="400px"
                  />
                </CardContent>
              </Card>
            )}

            {/* Pricing Calculator */}
            <PricingCalculator
              tourData={tourData}
              onPriceChange={(price) => updateTourData('totalPrice', price)}
            />

            {/* Book Now Section */}
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="p-6">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-orange-800 mb-2">Ready to Book Your Dream Safari?</h3>
                  <p className="text-orange-700 mb-6">
                    Your custom {tourData.duration}-day safari adventure awaits! Complete your booking to secure your dates.
                  </p>
                  <div className="flex items-center justify-center gap-4 mb-6">
                    <div className="flex items-center gap-2 text-orange-700">
                      <Clock className="h-4 w-4" />
                      <span className="text-sm">Instant confirmation</span>
                    </div>
                    <div className="flex items-center gap-2 text-orange-700">
                      <Star className="h-4 w-4" />
                      <span className="text-sm">Expert guides</span>
                    </div>
                  </div>
                  <Button 
                    onClick={handleBookNow}
                    disabled={isSubmitting || tourData.totalPrice === 0}
                    className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-8 py-3 text-lg"
                  >
                    {isSubmitting ? 'Creating Tour...' : `Book Now - $${tourData.totalPrice}`}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Custom Tour Builder</h1>
            <p className="text-xl max-w-2xl">
              Create your perfect safari experience with our interactive tour builder
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            {/* Steps Indicator */}
            <TourBuilderSteps 
              steps={steps} 
              currentStep={currentStep} 
              onStepChange={setCurrentStep}
            />

            {/* Step Content */}
            <div className="mt-8">
              {renderStepContent()}
            </div>

            {/* Navigation */}
            <div className="flex justify-between mt-8">
              <Button 
                onClick={prevStep} 
                disabled={currentStep === 1}
                variant="outline"
              >
                Previous
              </Button>
              
              {currentStep < steps.length ? (
                <Button 
                  onClick={nextStep}
                  className="bg-orange-600 hover:bg-orange-700"
                  disabled={
                    (currentStep === 1 && tourData.destinations.length === 0) ||
                    (currentStep === 2 && !tourData.accommodation) ||
                    (currentStep === 3 && tourData.activities.length === 0) ||
                    (currentStep === 4 && tourData.itinerary.length === 0)
                  }
                >
                  Next
                </Button>
              ) : null}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default TourBuilder;
