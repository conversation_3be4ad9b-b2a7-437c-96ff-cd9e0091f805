import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import ReviewsSection from '@/components/reviews/ReviewsSection';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Clock, MapPin, Star, Users, Heart, Share } from 'lucide-react';

interface TourDetail {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  groupSize: string;
  images: string[];
  category: string;
  accommodationLevel: string;
  destinations: string[];
  rating: number;
  reviews: number;
  inclusions: string[];
  exclusions: string[];
  itinerary: Array<{
    day: number;
    title: string;
    description: string;
    activities: string[];
    accommodation: string;
    meals: string;
  }>;
  gallery: string[];
}

const TourDetail = () => {
  const { id } = useParams();
  const [tour, setTour] = useState<TourDetail | null>(null);
  const [selectedImage, setSelectedImage] = useState(0);

  useEffect(() => {
    // Sample tour detail data
    const sampleTour: TourDetail = {
      id: id!,
      title: 'Serengeti Classic Safari',
      description: 'Embark on an unforgettable journey through the world-famous Serengeti National Park, where endless plains stretch as far as the eye can see. Witness the Great Migration, spot the Big Five, and experience the raw beauty of African wildlife in their natural habitat.',
      price: 2499,
      duration: '5 days, 4 nights',
      groupSize: 'Max 6 people',
      images: [
        'photo-1472396961693-142e6e269027',
        'photo-1466721591366-2d5fba72006d',
        'photo-1493962853295-0fd70327578a',
        'photo-1485833077593-4278bba3f11f'
      ],
      category: 'Wildlife Safari',
      accommodationLevel: 'Luxury',
      destinations: ['Serengeti National Park', 'Ngorongoro Crater'],
      rating: 4.8,
      reviews: 124,
      inclusions: [
        'Professional safari guide',
        'All park fees and permits',
        'Luxury safari vehicle with pop-up roof',
        '4 nights accommodation',
        'All meals as specified in itinerary',
        'Bottled water during game drives',
        'Airport transfers'
      ],
      exclusions: [
        'International flights',
        'Tanzania visa',
        'Travel insurance',
        'Personal expenses',
        'Tips and gratuities',
        'Alcoholic beverages'
      ],
      itinerary: [
        {
          day: 1,
          title: 'Arrival in Arusha',
          description: 'Arrive at Kilimanjaro Airport and transfer to your lodge in Arusha. Meet your safari guide and receive a briefing about your upcoming adventure.',
          activities: ['Airport pickup', 'Safari briefing', 'Equipment check'],
          accommodation: 'Mount Meru Hotel',
          meals: 'Dinner'
        },
        {
          day: 2,
          title: 'Arusha to Serengeti',
          description: 'Depart for Serengeti National Park with game viewing en route. Enter through Naabi Hill Gate and begin your first game drive.',
          activities: ['Game drive to Serengeti', 'Wildlife spotting', 'Sunset viewing'],
          accommodation: 'Serengeti Safari Lodge',
          meals: 'Breakfast, Lunch, Dinner'
        },
        {
          day: 3,
          title: 'Full Day Serengeti',
          description: 'Spend a full day exploring the vast Serengeti plains. Track the Great Migration and search for the Big Five.',
          activities: ['Morning game drive', 'Picnic lunch', 'Afternoon game drive'],
          accommodation: 'Serengeti Safari Lodge',
          meals: 'Breakfast, Lunch, Dinner'
        },
        {
          day: 4,
          title: 'Serengeti to Ngorongoro',
          description: 'Morning game drive in Serengeti, then drive to Ngorongoro Conservation Area with game viewing en route.',
          activities: ['Morning game drive', 'Transfer to Ngorongoro', 'Crater rim viewing'],
          accommodation: 'Ngorongoro Crater Lodge',
          meals: 'Breakfast, Lunch, Dinner'
        },
        {
          day: 5,
          title: 'Ngorongoro Crater & Departure',
          description: 'Descend into the Ngorongoro Crater for game viewing, then transfer back to Arusha for departure.',
          activities: ['Crater game drive', 'Cultural visit', 'Transfer to airport'],
          accommodation: 'Day use only',
          meals: 'Breakfast, Lunch'
        }
      ],
      gallery: [
        'photo-1472396961693-142e6e269027',
        'photo-1466721591366-2d5fba72006d',
        'photo-1493962853295-0fd70327578a',
        'photo-1485833077593-4278bba3f11f'
      ]
    };
    setTour(sampleTour);
  }, [id]);

  if (!tour) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Hero Image Gallery */}
        <div className="relative h-96 md:h-[500px]">
          <img
            src={`https://images.unsplash.com/${tour.images[selectedImage]}?auto=format&fit=crop&w=1200&h=500`}
            alt={tour.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40" />
          <div className="absolute bottom-4 left-4 right-4">
            <div className="flex space-x-2 overflow-x-auto">
              {tour.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 ${
                    selectedImage === index ? 'border-white' : 'border-transparent'
                  }`}
                >
                  <img
                    src={`https://images.unsplash.com/${image}?auto=format&fit=crop&w=100&h=100`}
                    alt={`${tour.title} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Tour Header */}
              <div className="mb-6">
                <div className="flex flex-wrap gap-2 mb-3">
                  <Badge variant="secondary">{tour.category}</Badge>
                  <Badge variant="outline">{tour.accommodationLevel}</Badge>
                </div>
                <h1 className="text-3xl md:text-4xl font-bold mb-4">{tour.title}</h1>
                <div className="flex flex-wrap gap-4 text-gray-600 mb-4">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    {tour.duration}
                  </div>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    {tour.groupSize}
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    {tour.destinations.join(', ')}
                  </div>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 mr-2 text-yellow-500" />
                    {tour.rating} ({tour.reviews} reviews)
                  </div>
                </div>
                <p className="text-lg text-gray-700">{tour.description}</p>
              </div>

              {/* Updated Tabs with Reviews */}
              <Tabs defaultValue="itinerary" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="itinerary">Itinerary</TabsTrigger>
                  <TabsTrigger value="inclusions">Inclusions</TabsTrigger>
                  <TabsTrigger value="gallery">Gallery</TabsTrigger>
                  <TabsTrigger value="reviews">Reviews</TabsTrigger>
                  <TabsTrigger value="virtual">Virtual Tour</TabsTrigger>
                </TabsList>

                <TabsContent value="itinerary" className="space-y-4">
                  {tour.itinerary.map((day, index) => (
                    <Card key={index}>
                      <CardHeader>
                        <CardTitle className="flex items-center">
                          <span className="bg-orange-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 text-sm">
                            {day.day}
                          </span>
                          {day.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-700 mb-3">{day.description}</p>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <strong>Activities:</strong>
                            <ul className="mt-1">
                              {day.activities.map((activity, i) => (
                                <li key={i} className="text-gray-600">• {activity}</li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <strong>Accommodation:</strong>
                            <p className="text-gray-600 mt-1">{day.accommodation}</p>
                          </div>
                          <div>
                            <strong>Meals:</strong>
                            <p className="text-gray-600 mt-1">{day.meals}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>

                <TabsContent value="inclusions" className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-green-600">What's Included</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {tour.inclusions.map((item, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-green-600 mr-2">✓</span>
                              {item}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-red-600">What's Not Included</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {tour.exclusions.map((item, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-red-600 mr-2">✗</span>
                              {item}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="gallery">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {tour.gallery.map((image, index) => (
                      <div key={index} className="aspect-square rounded-lg overflow-hidden">
                        <img
                          src={`https://images.unsplash.com/${image}?auto=format&fit=crop&w=400&h=400`}
                          alt={`Gallery ${index + 1}`}
                          className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                        />
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="reviews">
                  <ReviewsSection tourId={tour.id} tourName={tour.title} />
                </TabsContent>

                <TabsContent value="virtual">
                  <Card>
                    <CardContent className="p-6">
                      <div className="text-center mb-6">
                        <h3 className="text-2xl font-bold mb-2">Virtual Safari Experience</h3>
                        <p className="text-gray-600">Experience this destination in 360° before you visit</p>
                      </div>
                      <Link to="/virtual-tour">
                        <Button className="w-full bg-orange-600 hover:bg-orange-700 text-lg py-6">
                          Launch Virtual Tour
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            {/* Booking Sidebar */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="text-3xl font-bold text-orange-600">
                        ${tour.price.toLocaleString()}
                      </div>
                      <div className="text-gray-600">per person</div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="icon">
                        <Heart className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Share className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Link to={`/book/${tour.id}`}>
                    <Button className="w-full bg-orange-600 hover:bg-orange-700 text-lg py-6">
                      Book This Tour
                    </Button>
                  </Link>
                  
                  <Link to="/tour-builder">
                    <Button variant="outline" className="w-full">
                      Customize This Tour
                    </Button>
                  </Link>

                  <div className="border-t pt-4">
                    <h4 className="font-semibold mb-2">Need Help?</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Speak with our safari experts for personalized advice
                    </p>
                    <Button variant="outline" className="w-full">
                      <Calendar className="h-4 w-4 mr-2" />
                      Schedule a Call
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default TourDetail;
