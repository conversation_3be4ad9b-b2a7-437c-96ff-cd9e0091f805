import React, { useState, useEffect } from 'react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar, MapPin, Heart, User, Settings, FileText, Bell } from 'lucide-react';
import WishlistButton from '@/components/features/WishlistButton';
import { useWishlist } from '@/contexts/WishlistContext';

interface Booking {
  id: string;
  tourId: string;
  tourTitle: string;
  startDate: string;
  duration: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  image: string;
  totalPrice: number;
  guests: number;
}

const UserDashboard = () => {
  const { currentUser, userProfile, updateUserProfile } = useAuth();
  const { wishlist } = useWishlist();
  const [activeTab, setActiveTab] = useState('trips');
  const [userBookings, setUserBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(false);
  const [profileForm, setProfileForm] = useState({
    displayName: userProfile?.displayName || '',
    phone: userProfile?.phone || '',
    country: userProfile?.country || ''
  });

  useEffect(() => {
    if (currentUser) {
      fetchUserBookings();
    }
    if (userProfile) {
      setProfileForm({
        displayName: userProfile.displayName || '',
        phone: userProfile.phone || '',
        country: userProfile.country || ''
      });
    }
  }, [currentUser, userProfile]);

  const fetchUserBookings = async () => {
    if (!currentUser) return;
    
    setLoading(true);
    try {
      const bookingsQuery = query(
        collection(db, 'bookings'),
        where('userId', '==', currentUser.uid),
        orderBy('createdAt', 'desc')
      );
      const bookingsSnapshot = await getDocs(bookingsQuery);
      const bookingsData = bookingsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Booking[];
      setUserBookings(bookingsData);
    } catch (error) {
      console.error('Error fetching bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await updateUserProfile(profileForm);
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const upcomingTrips = userBookings.filter(booking => 
    booking.status === 'confirmed' && new Date(booking.startDate) > new Date()
  );

  const pastTrips = userBookings.filter(booking => 
    booking.status === 'confirmed' && new Date(booking.startDate) <= new Date()
  );

  if (!currentUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Please log in to access your dashboard</h2>
          <Button onClick={() => window.location.href = '/login'}>
            Go to Login
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl font-bold mb-4">Welcome back, {userProfile?.displayName}!</h1>
            <p className="text-xl">Manage your safari adventures</p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="trips">
                  <Calendar className="h-4 w-4 mr-2" />
                  Trips
                </TabsTrigger>
                <TabsTrigger value="wishlist">
                  <Heart className="h-4 w-4 mr-2" />
                  Wishlist
                </TabsTrigger>
                <TabsTrigger value="profile">
                  <User className="h-4 w-4 mr-2" />
                  Profile
                </TabsTrigger>
                <TabsTrigger value="documents">
                  <FileText className="h-4 w-4 mr-2" />
                  Documents
                </TabsTrigger>
                <TabsTrigger value="notifications">
                  <Bell className="h-4 w-4 mr-2" />
                  Notifications
                </TabsTrigger>
              </TabsList>

              <TabsContent value="trips" className="space-y-6">
                <div>
                  <h2 className="text-2xl font-bold mb-4">Upcoming Trips ({upcomingTrips.length})</h2>
                  {upcomingTrips.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {upcomingTrips.map((trip) => (
                        <Card key={trip.id}>
                          <div className="relative">
                            <img
                              src={trip.image}
                              alt={trip.tourTitle}
                              className="w-full h-48 object-cover rounded-t-lg"
                            />
                            <Badge className="absolute top-2 right-2 bg-green-600">
                              {trip.status}
                            </Badge>
                          </div>
                          <CardContent className="p-4">
                            <h3 className="font-semibold mb-2">{trip.tourTitle}</h3>
                            <div className="flex items-center text-sm text-gray-600 mb-2">
                              <Calendar className="h-4 w-4 mr-1" />
                              {trip.startDate} • {trip.duration}
                            </div>
                            <p className="text-lg font-bold text-orange-600 mb-2">${trip.totalPrice}</p>
                            <Button className="w-full mt-2">View Details</Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                        <h3 className="text-lg font-semibold mb-2">No upcoming trips</h3>
                        <p className="text-gray-600 mb-4">Book your next safari adventure</p>
                        <Button onClick={() => window.location.href = '/tours'}>Browse Tours</Button>
                      </CardContent>
                    </Card>
                  )}
                </div>

                <div>
                  <h2 className="text-2xl font-bold mb-4">Past Trips ({pastTrips.length})</h2>
                  {pastTrips.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {pastTrips.map((trip) => (
                        <Card key={trip.id}>
                          <div className="relative">
                            <img
                              src={trip.image}
                              alt={trip.tourTitle}
                              className="w-full h-48 object-cover rounded-t-lg"
                            />
                            <Badge className="absolute top-2 right-2 bg-blue-600">
                              {trip.status}
                            </Badge>
                          </div>
                          <CardContent className="p-4">
                            <h3 className="font-semibold mb-2">{trip.tourTitle}</h3>
                            <div className="flex items-center text-sm text-gray-600 mb-2">
                              <Calendar className="h-4 w-4 mr-1" />
                              {trip.startDate} • {trip.duration}
                            </div>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm" className="flex-1">Review</Button>
                              <Button variant="outline" size="sm" className="flex-1">Rebook</Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <p className="text-gray-600">No past trips yet</p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="wishlist" className="space-y-6">
                <h2 className="text-2xl font-bold mb-4">My Wishlist ({wishlist.length})</h2>
                {wishlist.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {wishlist.map((item) => (
                      <Card key={item.id}>
                        <div className="relative">
                          <img
                            src={`https://images.unsplash.com/${item.image}?auto=format&fit=crop&w=400&h=200`}
                            alt={item.title}
                            className="w-full h-48 object-cover rounded-t-lg"
                          />
                          <Badge className="absolute top-2 right-2 capitalize">
                            {item.type}
                          </Badge>
                        </div>
                        <CardContent className="p-4">
                          <h3 className="font-semibold mb-2">{item.title}</h3>
                          <p className="text-lg font-bold text-orange-600 mb-3">${item.price}</p>
                          <div className="flex gap-2">
                            <Button size="sm" className="flex-1">Book Now</Button>
                            <WishlistButton item={item} size="sm" />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Heart className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Your wishlist is empty</h3>
                      <p className="text-gray-600 mb-4">Start adding tours and destinations you'd like to visit</p>
                      <Button onClick={() => window.location.href = '/tours'}>Explore Tours</Button>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="profile" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleProfileUpdate} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="displayName">Full Name</Label>
                          <Input
                            id="displayName"
                            value={profileForm.displayName}
                            onChange={(e) => setProfileForm({...profileForm, displayName: e.target.value})}
                          />
                        </div>
                        <div>
                          <Label htmlFor="email">Email</Label>
                          <Input
                            id="email"
                            value={currentUser?.email || ''}
                            disabled
                            className="bg-gray-100"
                          />
                        </div>
                        <div>
                          <Label htmlFor="phone">Phone</Label>
                          <Input
                            id="phone"
                            value={profileForm.phone}
                            onChange={(e) => setProfileForm({...profileForm, phone: e.target.value})}
                          />
                        </div>
                        <div>
                          <Label htmlFor="country">Country</Label>
                          <Input
                            id="country"
                            value={profileForm.country}
                            onChange={(e) => setProfileForm({...profileForm, country: e.target.value})}
                          />
                        </div>
                      </div>
                      <Button type="submit" disabled={loading}>
                        {loading ? 'Updating...' : 'Update Profile'}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="documents" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Travel Documents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="border rounded-lg p-4">
                        <h4 className="font-semibold mb-2">Passport</h4>
                        <p className="text-sm text-gray-600 mb-2">Upload your passport for visa processing</p>
                        <Button variant="outline" size="sm">Upload Document</Button>
                      </div>
                      <div className="border rounded-lg p-4">
                        <h4 className="font-semibold mb-2">Travel Insurance</h4>
                        <p className="text-sm text-gray-600 mb-2">Required for all safari tours</p>
                        <Button variant="outline" size="sm">Upload Document</Button>
                      </div>
                      <div className="border rounded-lg p-4">
                        <h4 className="font-semibold mb-2">Medical Certificates</h4>
                        <p className="text-sm text-gray-600 mb-2">Yellow fever vaccination required</p>
                        <Button variant="outline" size="sm">Upload Document</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="notifications" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification Preferences</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Email Notifications</h4>
                        <p className="text-sm text-gray-600">Receive updates about your bookings</p>
                      </div>
                      <input type="checkbox" defaultChecked className="toggle" />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">SMS Notifications</h4>
                        <p className="text-sm text-gray-600">Get text updates for urgent matters</p>
                      </div>
                      <input type="checkbox" defaultChecked className="toggle" />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Marketing Emails</h4>
                        <p className="text-sm text-gray-600">Special offers and new tour announcements</p>
                      </div>
                      <input type="checkbox" className="toggle" />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default UserDashboard;
