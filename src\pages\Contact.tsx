
import React, { useState, useEffect, useRef } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Phone, Mail, Clock, Send, CheckCircle, MessageSquare, Globe } from 'lucide-react';

const Contact = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const parallaxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (parallaxRef.current) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.3;
        parallaxRef.current.style.transform = `translateY(${rate}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Simulate form submission
    setIsSubmitted(true);
    setTimeout(() => setIsSubmitted(false), 3000);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value
    });
  };

  const contactMethods = [
    {
      icon: Phone,
      title: 'Call Us',
      details: ['+255 784 123 456', '+255 765 987 654 (WhatsApp)'],
      description: 'Available 8AM - 5PM EAT, Mon-Fri',
      color: 'bg-blue-500'
    },
    {
      icon: Mail,
      title: 'Email Us',
      details: ['<EMAIL>', '<EMAIL>'],
      description: 'We respond within 24 hours',
      color: 'bg-green-500'
    },
    {
      icon: MapPin,
      title: 'Visit Us',
      details: ['Arusha Business Center', '302 Safari Avenue, Arusha'],
      description: 'Open Mon-Sat, 8AM-5PM',
      color: 'bg-orange-500'
    },
    {
      icon: MessageSquare,
      title: 'Live Chat',
      details: ['24/7 Support Available', 'Instant Response'],
      description: 'Chat with our safari experts',
      color: 'bg-purple-500'
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section with Parallax */}
        <div className="relative h-96 overflow-hidden">
          <div 
            ref={parallaxRef}
            className="absolute inset-0 w-full h-120"
            style={{
              backgroundImage: 'url(https://images.unsplash.com/photo-1485833077593-4278bba3f11f?auto=format&fit=crop&w=1920&h=1080)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-orange-600/80 to-red-600/80" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-3xl mx-auto">
              <Badge className="mb-4 bg-white/20 text-white px-4 py-2">
                <Globe className="w-4 h-4 mr-2" />
                Get In Touch
              </Badge>
              <h1 className="text-5xl md:text-6xl font-bold mb-6">Contact Us</h1>
              <p className="text-xl md:text-2xl leading-relaxed opacity-90">
                Ready to plan your dream safari? Our expert team is here to help you create the adventure of a lifetime.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Methods */}
        <div className="py-16 bg-gradient-to-b from-gray-50 to-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">How Can We Help?</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Choose your preferred way to connect with our safari specialists
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {contactMethods.map((method, index) => (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-0 shadow-lg">
                  <CardContent className="p-6 text-center">
                    <div className={`${method.color} w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                      <method.icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{method.title}</h3>
                    <div className="space-y-1 mb-3">
                      {method.details.map((detail, i) => (
                        <p key={i} className="text-sm font-medium text-gray-700">{detail}</p>
                      ))}
                    </div>
                    <p className="text-xs text-gray-500">{method.description}</p>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-4 group-hover:bg-orange-600 group-hover:text-white group-hover:border-orange-600 transition-colors"
                    >
                      Connect Now
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Contact Content */}
        <div className="container mx-auto px-4 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <Card className="shadow-2xl border-0">
                <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
                  <CardTitle className="flex items-center text-2xl">
                    <Send className="mr-3 h-6 w-6" />
                    Send Us a Message
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  {isSubmitted ? (
                    <div className="text-center py-8">
                      <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                      <h3 className="text-2xl font-semibold text-green-600 mb-2">Message Sent!</h3>
                      <p className="text-gray-600">Thank you for contacting us. We'll get back to you within 24 hours.</p>
                    </div>
                  ) : (
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="firstName" className="text-sm font-semibold">First Name *</Label>
                          <Input 
                            id="firstName" 
                            placeholder="Enter your first name" 
                            value={formData.firstName}
                            onChange={handleInputChange}
                            className="border-2 focus:border-orange-500 transition-colors"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName" className="text-sm font-semibold">Last Name *</Label>
                          <Input 
                            id="lastName" 
                            placeholder="Enter your last name" 
                            value={formData.lastName}
                            onChange={handleInputChange}
                            className="border-2 focus:border-orange-500 transition-colors"
                            required
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-semibold">Email Address *</Label>
                        <Input 
                          id="email" 
                          type="email" 
                          placeholder="<EMAIL>" 
                          value={formData.email}
                          onChange={handleInputChange}
                          className="border-2 focus:border-orange-500 transition-colors"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-sm font-semibold">Phone Number</Label>
                        <Input 
                          id="phone" 
                          placeholder="+****************" 
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="border-2 focus:border-orange-500 transition-colors"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="subject" className="text-sm font-semibold">Subject *</Label>
                        <Input 
                          id="subject" 
                          placeholder="What can we help you with?" 
                          value={formData.subject}
                          onChange={handleInputChange}
                          className="border-2 focus:border-orange-500 transition-colors"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="message" className="text-sm font-semibold">Message *</Label>
                        <Textarea
                          id="message"
                          placeholder="Tell us about your dream safari experience..."
                          rows={5}
                          value={formData.message}
                          onChange={handleInputChange}
                          className="border-2 focus:border-orange-500 transition-colors resize-none"
                          required
                        />
                      </div>
                      <Button 
                        type="submit"
                        className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all"
                      >
                        <Send className="mr-2 h-5 w-5" />
                        Send Message
                      </Button>
                    </form>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Contact Information & Map */}
            <div className="space-y-8">
              <Card className="shadow-xl border-0">
                <CardHeader className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-t-lg">
                  <CardTitle className="flex items-center text-2xl">
                    <MapPin className="mr-3 h-6 w-6" />
                    Visit Our Office
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <MapPin className="h-6 w-6 text-orange-600 mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-lg mb-2">Our Office</h3>
                        <p className="text-gray-600 leading-relaxed">
                          Arusha Business Center<br />
                          302 Safari Avenue<br />
                          Arusha, Tanzania
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-4">
                      <Phone className="h-6 w-6 text-orange-600 mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-lg mb-2">Phone</h3>
                        <p className="text-gray-600">
                          +255 784 123 456<br />
                          +255 765 987 654 (WhatsApp)
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-4">
                      <Mail className="h-6 w-6 text-orange-600 mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-lg mb-2">Email</h3>
                        <p className="text-gray-600">
                          <EMAIL><br />
                          <EMAIL>
                        </p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-4">
                      <Clock className="h-6 w-6 text-orange-600 mt-1 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-lg mb-2">Hours</h3>
                        <p className="text-gray-600">
                          Monday - Friday: 8AM - 5PM EAT<br />
                          Saturday: 9AM - 1PM EAT<br />
                          Sunday: Closed
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Interactive Map Placeholder */}
              <Card className="shadow-xl border-0">
                <CardContent className="p-0">
                  <div className="bg-gradient-to-br from-orange-100 to-red-100 h-64 rounded-lg flex items-center justify-center text-gray-600 relative overflow-hidden">
                    <div className="text-center z-10">
                      <MapPin className="h-12 w-12 mx-auto mb-4 text-orange-600" />
                      <h3 className="text-lg font-semibold mb-2">Interactive Map</h3>
                      <p className="text-sm">Google Maps integration will be displayed here</p>
                      <Button className="mt-4 bg-orange-600 hover:bg-orange-700">
                        View Directions
                      </Button>
                    </div>
                    <div className="absolute inset-0 opacity-10">
                      <div className="grid grid-cols-8 grid-rows-8 h-full">
                        {Array.from({ length: 64 }).map((_, i) => (
                          <div key={i} className="border border-orange-300"></div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Response Promise */}
              <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 shadow-lg">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <CheckCircle className="h-8 w-8 text-green-600 flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-lg mb-2 text-green-800">Quick Response Promise</h3>
                      <p className="text-green-700 leading-relaxed">
                        We respond to all inquiries within 24 hours during business hours. For urgent safari bookings or questions, please call our direct line for immediate assistance.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Social Media Section */}
        <div className="bg-gradient-to-r from-gray-900 to-gray-800 py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Follow Our Safari Adventures</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Stay connected and see the latest wildlife sightings, guest stories, and behind-the-scenes moments
            </p>
            <div className="flex justify-center space-x-6">
              {[
                { name: 'Facebook', users: '25K' },
                { name: 'Instagram', users: '40K' },
                { name: 'Twitter', users: '15K' },
                { name: 'YouTube', users: '10K' }
              ].map((social, index) => (
                <Button 
                  key={index}
                  variant="outline" 
                  className="border-white text-white hover:bg-white hover:text-gray-900 transition-colors group"
                >
                  <div className="text-center">
                    <div className="font-semibold">{social.name}</div>
                    <div className="text-xs opacity-75">{social.users} followers</div>
                  </div>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Contact;
