
import { useState, useEffect } from 'react';
import { TourService } from '@/services/tourService';
import { Tour, SearchFilters } from '@/types/firebase';

export const useTours = (filters?: SearchFilters) => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadTours = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await TourService.getTours(filters);
      setTours(data);
    } catch (err) {
      setError('Failed to load tours');
      console.error('Error loading tours:', err);
    } finally {
      setLoading(false);
    }
  };

  const searchTours = async (searchTerm: string, searchFilters?: SearchFilters) => {
    setLoading(true);
    setError(null);
    try {
      const data = await TourService.searchTours(searchTerm, searchFilters);
      setTours(data);
    } catch (err) {
      setError('Failed to search tours');
      console.error('Error searching tours:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTours();
  }, []);

  return {
    tours,
    loading,
    error,
    refetch: loadTours,
    searchTours
  };
};

export const useFeaturedTours = (limit: number = 6) => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadFeaturedTours = async () => {
      try {
        const data = await TourService.getFeaturedTours(limit);
        setTours(data);
      } catch (error) {
        console.error('Error loading featured tours:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedTours();
  }, [limit]);

  return { tours, loading };
};
