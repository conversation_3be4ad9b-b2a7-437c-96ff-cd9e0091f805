import React, { useState } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import VirtualTour from '@/components/features/VirtualTour';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MapPin, Clock, Camera, Users, Star, Calendar } from 'lucide-react';

const VirtualTourPage = () => {
  const [selectedDestination, setSelectedDestination] = useState('serengeti');

  const destinations = {
    serengeti: {
      name: 'Serengeti National Park',
      description: 'Experience the endless plains of the Serengeti, home to the Great Migration and incredible wildlife diversity.',
      images: [
        'photo-1472396961693-142e6e269027',
        'photo-1466721591366-2d5fba72006d',
        'photo-1493962853295-0fd70327578a',
        'photo-1485833077593-4278bba3f11f'
      ],
      highlights: [
        'Great Migration viewing',
        'Big Five wildlife spotting',
        'Endless savanna plains',
        'Traditional Maasai culture'
      ],
      bestTime: 'June to October',
      duration: '3-7 days',
      difficulty: 'Easy',
      rating: 4.9
    },
    ngorongoro: {
      name: 'Ngorongoro Crater',
      description: 'Descend into the world\'s largest intact volcanic caldera, a UNESCO World Heritage site teeming with wildlife.',
      images: [
        'photo-1547036967-23d11aacaee0',
        'photo-1516426122078-c23e76319801',
        'photo-1564420962775-c14a5dd2e2b4',
        'photo-1580407196238-dac33f57c410'
      ],
      highlights: [
        'Volcanic crater ecosystem',
        'Dense wildlife population',
        'Flamingo-filled lakes',
        'Ancient geological formations'
      ],
      bestTime: 'Year-round',
      duration: '1-2 days',
      difficulty: 'Moderate',
      rating: 4.8
    },
    kilimanjaro: {
      name: 'Mount Kilimanjaro',
      description: 'Africa\'s highest peak offers breathtaking views and diverse ecosystems from rainforest to alpine desert.',
      images: [
        'photo-1609198092458-38a293c7ac4b',
        'photo-1544947950-fa07a98d237f',
        'photo-1546026423-cc4642628d2b',
        'photo-1566065142742-3c7090c23e9e'
      ],
      highlights: [
        'Snow-capped summit',
        'Multiple climate zones',
        'Unique alpine flora',
        'Challenging trekking routes'
      ],
      bestTime: 'January-March, June-October',
      duration: '5-9 days',
      difficulty: 'Challenging',
      rating: 4.7
    }
  };

  const currentDestination = destinations[selectedDestination as keyof typeof destinations];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Virtual Safari Tours</h1>
            <p className="text-xl max-w-3xl">
              Explore Tanzania's most iconic destinations from the comfort of your home with our immersive 360° virtual tours
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          {/* Destination Selector */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Choose Your Virtual Destination</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(destinations).map(([key, dest]) => (
                  <div
                    key={key}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedDestination === key 
                        ? 'border-orange-500 bg-orange-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedDestination(key)}
                  >
                    <img
                      src={`https://images.unsplash.com/${dest.images[0]}?auto=format&fit=crop&w=300&h=200`}
                      alt={dest.name}
                      className="w-full h-32 object-cover rounded mb-3"
                    />
                    <h3 className="font-semibold mb-2">{dest.name}</h3>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-500 mr-1" />
                        {dest.rating}
                      </span>
                      <Badge variant={dest.difficulty === 'Easy' ? 'default' : dest.difficulty === 'Moderate' ? 'secondary' : 'destructive'}>
                        {dest.difficulty}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Virtual Tour */}
            <div className="lg:col-span-2">
              <VirtualTour 
                destination={currentDestination.name}
                images={currentDestination.images}
              />
            </div>

            {/* Destination Info */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-orange-600" />
                    {currentDestination.name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">{currentDestination.description}</p>
                  
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="text-sm">Best Time: {currentDestination.bestTime}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="text-sm">Duration: {currentDestination.duration}</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="text-sm">Difficulty: {currentDestination.difficulty}</span>
                    </div>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 mr-2 text-yellow-500" />
                      <span className="text-sm">Rating: {currentDestination.rating}/5</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Highlights</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {currentDestination.highlights.map((highlight, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-orange-600 mr-2">•</span>
                        <span className="text-sm">{highlight}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Plan Your Visit</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full">
                    Book Real Safari Tour
                  </Button>
                  <Button variant="outline" className="w-full">
                    Download VR Experience
                  </Button>
                  <Button variant="outline" className="w-full">
                    Share Virtual Tour
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Additional Information */}
          <div className="mt-12">
            <Tabs defaultValue="wildlife" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="wildlife">Wildlife</TabsTrigger>
                <TabsTrigger value="photography">Photography Tips</TabsTrigger>
                <TabsTrigger value="conservation">Conservation</TabsTrigger>
                <TabsTrigger value="planning">Planning</TabsTrigger>
              </TabsList>

              <TabsContent value="wildlife" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Wildlife You Can Spot</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {['Lions', 'Elephants', 'Leopards', 'Rhinos', 'Buffalo', 'Cheetahs', 'Giraffes', 'Zebras'].map((animal) => (
                        <div key={animal} className="text-center p-3 border rounded-lg">
                          <div className="text-2xl mb-2">🦁</div>
                          <div className="text-sm font-medium">{animal}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="photography" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Photography Tips</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <Camera className="h-5 w-5 text-orange-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium">Golden Hour Magic</h4>
                          <p className="text-sm text-gray-600">Best lighting occurs during sunrise and sunset for dramatic wildlife photography.</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <Camera className="h-5 w-5 text-orange-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium">Telephoto Lens</h4>
                          <p className="text-sm text-gray-600">Use 300-600mm lenses to capture wildlife from a safe distance.</p>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <Camera className="h-5 w-5 text-orange-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium">Patience is Key</h4>
                          <p className="text-sm text-gray-600">Wait for natural behaviors and unique moments rather than just portraits.</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="conservation" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Conservation Efforts</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">
                      Learn how your visit contributes to wildlife conservation and local community development in Tanzania.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 bg-green-50 rounded-lg">
                        <h4 className="font-semibold text-green-800 mb-2">Wildlife Protection</h4>
                        <p className="text-sm text-green-700">Anti-poaching initiatives and habitat preservation programs.</p>
                      </div>
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <h4 className="font-semibold text-blue-800 mb-2">Community Support</h4>
                        <p className="text-sm text-blue-700">Tourism revenue supports local Maasai communities and education.</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="planning" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Planning Your Safari</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold mb-2">When to Visit</h4>
                        <p className="text-sm text-gray-600 mb-2">
                          {currentDestination.bestTime} offers the best weather and wildlife viewing opportunities.
                        </p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">What to Pack</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• Neutral-colored clothing</li>
                          <li>• Binoculars and camera</li>
                          <li>• Sun protection and insect repellent</li>
                          <li>• Comfortable walking shoes</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default VirtualTourPage;
