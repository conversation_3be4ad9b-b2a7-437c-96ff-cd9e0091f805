import { FirebaseService } from '@/services/firebase';

export const seedInitialData = async () => {
  try {
    console.log('Starting to seed initial data...');

    // Seed Destinations
    const destinations = [
      {
        name: 'Serengeti National Park',
        description: 'Home to the Great Migration and abundant wildlife',
        country: 'Tanzania',
        region: 'Northern Tanzania',
        coordinates: { lat: -2.1540, lng: 34.6857 },
        bestTimeToVisit: ['June', 'July', 'August', 'September', 'October'],
        climate: 'Semi-arid with dry and wet seasons',
        wildlife: [
          {
            species: 'African Lion',
            scientificName: 'Panthera leo',
            category: 'big-five' as const,
            abundance: 'moderate' as const,
            bestSpottingTime: 'Early morning and late afternoon',
            behavior: 'Social cats living in prides, most active during cooler hours',
            conservationStatus: 'Vulnerable',
            photographyTips: 'Use telephoto lens, respect minimum distance of 25 meters'
          },
          {
            species: 'African Elephant',
            scientificName: 'Loxodonta africana',
            category: 'big-five' as const,
            abundance: 'common' as const,
            bestSpottingTime: 'Throughout the day near water sources',
            behavior: 'Highly intelligent, live in matriarchal herds',
            conservationStatus: 'Endangered',
            photographyTips: 'Capture family interactions, use wide-angle for herd shots'
          },
          {
            species: 'Wildebeest',
            scientificName: 'Connochaetes taurinus',
            category: 'herbivore' as const,
            abundance: 'common' as const,
            bestSpottingTime: 'During migration months',
            behavior: 'Migratory herds following rainfall patterns',
            conservationStatus: 'Least Concern'
          }
        ],
        images: ['/placeholder.svg'],
        activities: ['Game drives', 'Hot air balloon', 'Walking safari'],
        accommodations: ['Luxury lodges', 'Tented camps', 'Mobile camps'],
        featured: true,
        detailedGuide: {
          overview: 'The Serengeti is Tanzania\'s most famous national park and home to the spectacular Great Migration.',
          geography: 'Vast savannah plains covering 14,750 square kilometers',
          history: 'Established in 1951, UNESCO World Heritage Site since 1981',
          bestTimeToVisit: {
            drySeason: 'June to October - best for game viewing and migration',
            greenSeason: 'November to May - calving season and lush landscapes',
            photography: 'June to October for wildlife, February for calving',
            birding: 'November to April when migratory birds are present'
          },
          gettingThere: 'Accessible by road from Arusha (4-5 hours) or charter flights to various airstrips',
          accommodation: 'Wide range from budget camping to ultra-luxury lodges',
          packingTips: ['Neutral colored clothing', 'Sun protection', 'Binoculars', 'Camera with telephoto lens'],
          healthSafety: 'Malaria precautions required, travel insurance recommended',
          travelTips: ['Book accommodation well in advance', 'Bring cash for park fees', 'Respect wildlife viewing distances']
        },
        seasonalInfo: {
          drySeason: {
            months: ['June', 'July', 'August', 'September', 'October'],
            description: 'Clear skies, minimal rainfall, excellent game viewing',
            wildlife: 'Animals concentrate around water sources, Great Migration river crossings',
            photography: 'Perfect lighting conditions, dramatic skies, dust clouds during migration',
            advantages: ['Excellent game viewing', 'Clear weather', 'Migration river crossings'],
            disadvantages: ['Higher prices', 'More crowded', 'Dusty conditions']
          },
          greenSeason: {
            months: ['November', 'December', 'January', 'February', 'March', 'April', 'May'],
            description: 'Lush green landscapes, calving season, fewer crowds',
            wildlife: 'Calving season (December-March), resident wildlife, migratory birds',
            photography: 'Dramatic storm clouds, lush landscapes, baby animals',
            advantages: ['Lower prices', 'Fewer crowds', 'Calving season', 'Lush scenery'],
            disadvantages: ['Occasional heavy rains', 'Some roads may be challenging', 'Animals more dispersed']
          }
        },
        conservationInfo: {
          initiatives: ['Anti-poaching efforts', 'Community conservation programs', 'Research initiatives'],
          challenges: ['Human-wildlife conflict', 'Poaching pressure', 'Climate change impacts'],
          howTouristsHelp: ['Park fees fund conservation', 'Support local communities', 'Awareness raising'],
          conservationFee: 70
        },
        culturalInfo: {
          tribes: ['Maasai'],
          languages: ['Swahili', 'Maasai'],
          traditions: ['Traditional Maasai culture', 'Warrior traditions', 'Cattle herding'],
          etiquette: ['Respect Maasai customs', 'Ask permission before photographing people', 'Remove shoes when entering homes'],
          culturalSites: ['Maasai villages', 'Rock paintings', 'Traditional ceremonies']
        }
      }
    ];

    // Seed Activities with proper types
    const activities = [
      {
        name: 'Game Drive',
        description: 'Classic safari experience in open vehicles',
        category: 'wildlife' as const,
        difficulty: 'easy' as const,
        duration: '3-4 hours',
        minAge: 5,
        maxGroupSize: 8,
        equipment: ['Binoculars', 'Camera', 'Sun hat'],
        price: 150,
        destinations: ['Serengeti', 'Ngorongoro'],
        images: ['/placeholder.svg'],
        seasonal: false,
        fitnessRequired: 'low' as const,
        specialRequirements: ['Valid passport', 'Yellow fever certificate if applicable'],
        wildlifeSpotting: [
          {
            species: 'Big Five',
            probability: 'likely' as const,
            season: 'year-round' as const,
            bestTime: 'Early morning and late afternoon'
          }
        ],
        photographyOpportunities: ['Wildlife portraits', 'Action shots', 'Landscape photography'],
        conservationAspect: 'Learn about wildlife conservation efforts and anti-poaching initiatives'
      },
      {
        name: 'Walking Safari',
        description: 'Guided walks in the wilderness',
        category: 'adventure' as const,
        difficulty: 'moderate' as const,
        duration: '2-3 hours',
        minAge: 12,
        maxGroupSize: 6,
        equipment: ['Walking boots', 'Water bottle', 'Daypack'],
        price: 100,
        destinations: ['Serengeti', 'Tarangire'],
        images: ['/placeholder.svg'],
        seasonal: false,
        fitnessRequired: 'moderate' as const,
        specialRequirements: ['Good walking boots', 'Reasonable fitness level'],
        wildlifeSpotting: [
          {
            species: 'Small wildlife and birds',
            probability: 'very-likely' as const,
            season: 'year-round' as const,
            bestTime: 'Early morning'
          }
        ],
        photographyOpportunities: ['Close-up nature photography', 'Bird photography', 'Landscape details'],
        conservationAspect: 'Understanding ecosystem balance and tracking skills'
      },
      {
        name: 'Photography Safari',
        description: 'Specialized safari for photography enthusiasts',
        category: 'photography' as const,
        difficulty: 'easy' as const,
        duration: '6-8 hours',
        minAge: 16,
        maxGroupSize: 4,
        equipment: ['Professional camera gear', 'Telephoto lens', 'Tripod'],
        price: 300,
        destinations: ['Serengeti', 'Ngorongoro'],
        images: ['/placeholder.svg'],
        seasonal: false,
        fitnessRequired: 'low' as const,
        specialRequirements: ['Own camera equipment', 'Photography experience helpful'],
        wildlifeSpotting: [
          {
            species: 'All safari wildlife',
            probability: 'very-likely' as const,
            season: 'year-round' as const,
            bestTime: 'Golden hours'
          }
        ],
        photographyOpportunities: ['Professional wildlife photography', 'Behavior documentation', 'Portfolio building'],
        conservationAspect: 'Photography for conservation awareness and documentation'
      },
      {
        name: 'Cultural Experience',
        description: 'Visit local communities and learn about traditions',
        category: 'cultural' as const,
        difficulty: 'easy' as const,
        duration: '4-5 hours',
        minAge: 8,
        maxGroupSize: 10,
        equipment: ['Comfortable walking shoes', 'Cultural gifts'],
        price: 80,
        destinations: ['Maasai Villages'],
        images: ['/placeholder.svg'],
        seasonal: false,
        fitnessRequired: 'low' as const,
        specialRequirements: ['Respectful attitude', 'Cultural sensitivity'],
        wildlifeSpotting: [
          {
            species: 'Domestic animals',
            probability: 'guaranteed' as const,
            season: 'year-round' as const,
            bestTime: 'Anytime'
          }
        ],
        photographyOpportunities: ['Cultural photography', 'Portrait photography', 'Traditional crafts'],
        culturalSignificance: 'Learn about Maasai traditions, customs, and daily life'
      }
    ];

    // Create destinations
    for (const destination of destinations) {
      await FirebaseService.createDestination(destination);
    }

    // Seed Activities with proper types
    for (const activity of activities) {
      await FirebaseService.createActivity(activity);
    }

    // Seed Accommodations with proper types
    const accommodations = [
      {
        name: 'Serengeti Safari Lodge',
        description: 'Luxury lodge overlooking the Serengeti plains',
        type: 'lodge' as const,
        category: 'luxury' as const,
        location: 'Central Serengeti',
        coordinates: { lat: -2.1540, lng: 34.6857 },
        amenities: ['Swimming pool', 'Spa', 'Restaurant', 'Bar', 'WiFi'],
        roomTypes: [
          {
            name: 'Deluxe Room',
            description: 'Spacious room with savannah views',
            maxOccupancy: 2,
            price: 400,
            amenities: ['King bed', 'Private bathroom', 'Balcony'],
            images: ['/placeholder.svg'],
            wildlifeViewing: true
          }
        ],
        images: ['/placeholder.svg'],
        rating: 4.8,
        reviewCount: 127,
        pricePerNight: 400,
        maxGuests: 80,
        sustainability: {
          ecoFriendly: true,
          localCommunitySupport: true,
          conservationEfforts: ['Solar power', 'Water conservation', 'Waste management'],
          sustainablePractices: ['Local sourcing', 'Cultural programs', 'Wildlife corridor protection'],
          certifications: ['EarthCheck Gold', 'Fair Trade Tourism']
        },
        wildlifeViewing: true,
        photographyFacilities: ['Photo hides', 'Equipment rental', 'Charging stations'],
        conservationPrograms: ['Wildlife research support', 'Anti-poaching funding'],
        culturalExperiences: ['Maasai cultural programs', 'Traditional dance performances'],
        accessibility: ['Wheelchair accessible rooms', 'Accessible pathways']
      },
      {
        name: 'Serengeti Tented Camp',
        description: 'Authentic tented camp experience',
        type: 'camp' as const,
        category: 'midrange' as const,
        location: 'Northern Serengeti',
        coordinates: { lat: -1.8540, lng: 34.7857 },
        amenities: ['Restaurant', 'Campfire area', 'Guided walks'],
        roomTypes: [
          {
            name: 'Safari Tent',
            description: 'Comfortable tent with en-suite bathroom',
            maxOccupancy: 2,
            price: 200,
            amenities: ['Twin beds', 'Private bathroom', 'Mosquito nets'],
            images: ['/placeholder.svg'],
            wildlifeViewing: true
          }
        ],
        images: ['/placeholder.svg'],
        rating: 4.3,
        reviewCount: 89,
        pricePerNight: 200,
        maxGuests: 32,
        sustainability: {
          ecoFriendly: true,
          localCommunitySupport: true,
          conservationEfforts: ['Minimal environmental impact', 'Wildlife corridor preservation'],
          sustainablePractices: ['Local employment', 'Community projects'],
          certifications: ['Responsible Tourism Tanzania']
        },
        wildlifeViewing: true,
        photographyFacilities: ['Basic equipment storage'],
        conservationPrograms: ['Community conservation projects'],
        culturalExperiences: ['Local guide programs'],
        accessibility: ['Limited accessibility']
      }
    ];

    for (const accommodation of accommodations) {
      await FirebaseService.createAccommodation(accommodation);
    }

    // Seed Tours with all required properties
    const tours = [
      {
        title: '5-Day Serengeti Safari',
        description: 'Experience the best of Serengeti with expert guides',
        price: 2500,
        duration: '5 days',
        location: 'Serengeti National Park',
        destinations: ['Serengeti National Park'],
        activities: ['Game drives', 'Walking safari', 'Cultural visit'],
        accommodations: ['Serengeti Safari Lodge'],
        maxGroupSize: 8,
        minGroupSize: 2,
        difficulty: 'moderate' as const,
        includes: ['Accommodation', 'Meals', 'Game drives', 'Park fees'],
        excludes: ['International flights', 'Visa fees', 'Travel insurance'],
        images: ['/placeholder.svg'],
        featured: true,
        status: 'active' as const,
        rating: 4.8,
        reviewCount: 124,
        tourType: 'standard' as const,
        seasonality: {
          greenSeason: true,
          drySeason: true,
          bestMonths: ['June', 'July', 'August', 'September', 'October', 'January', 'February']
        },
        itinerary: [
          {
            day: 1,
            title: 'Arrival and Evening Game Drive',
            description: 'Arrive at Serengeti, check-in and afternoon game drive',
            accommodation: 'Serengeti Safari Lodge',
            meals: ['Lunch', 'Dinner'],
            activities: [
              {
                time: '14:00',
                activity: 'Arrival and Check-in',
                description: 'Welcome briefing and lodge orientation',
                duration: '1 hour',
                location: 'Serengeti Safari Lodge'
              },
              {
                time: '16:00',
                activity: 'Evening Game Drive',
                description: 'First safari experience in the Central Serengeti',
                duration: '3 hours',
                location: 'Central Serengeti'
              }
            ],
            drivingTime: '30 minutes from airstrip',
            highlights: ['First wildlife sightings', 'Sunset over the plains']
          }
        ],
        fitnessRequirements: {
          level: 'moderate' as const,
          description: 'Requires ability to get in/out of safari vehicles and walk short distances',
          walkingDistance: '1-2 km per day',
          terrain: 'Mostly flat savannah with some uneven ground',
          ageRestrictions: 'Suitable for ages 8 and above',
          medicalConditions: ['Not suitable for severe mobility issues']
        },
        equipment: {
          provided: [
            {
              name: 'Safari Vehicle',
              description: 'Open-sided 4WD vehicle with pop-up roof',
              category: 'gear' as const,
              optional: false
            },
            {
              name: 'Binoculars',
              description: 'Quality binoculars for wildlife viewing',
              category: 'gear' as const,
              optional: false
            }
          ],
          recommended: [
            {
              name: 'Camera with Telephoto Lens',
              description: 'For wildlife photography',
              category: 'photography' as const,
              optional: true
            },
            {
              name: 'Sun Hat',
              description: 'Protection from African sun',
              category: 'clothing' as const,
              optional: true
            }
          ],
          required: [
            {
              name: 'Passport',
              description: 'Valid passport with 6 months validity',
              category: 'safety' as const,
              optional: false
            },
            {
              name: 'Yellow Fever Certificate',
              description: 'Required if traveling from endemic areas',
              category: 'safety' as const,
              optional: false
            }
          ]
        },
        groupOptions: [
          {
            type: 'shared' as const,
            minParticipants: 2,
            maxParticipants: 8,
            pricePerPerson: 2500,
            description: 'Join other travelers for a shared safari experience'
          },
          {
            type: 'private' as const,
            minParticipants: 2,
            maxParticipants: 6,
            pricePerPerson: 3200,
            description: 'Private safari with dedicated guide and vehicle'
          }
        ],
        specialFeatures: ['Expert naturalist guides', 'Guaranteed window seats', 'Complimentary airport transfers'],
        difficultyDetails: 'This safari involves daily game drives in safari vehicles over sometimes bumpy terrain. Guests should be able to climb in and out of vehicles and walk short distances.'
      }
    ];

    for (const tour of tours) {
      await FirebaseService.createTour(tour);
    }

    console.log('Initial data seeded successfully!');
  } catch (error) {
    console.error('Error seeding data:', error);
  }
};
