import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // 🦁 SAVANNAH WILDLIFE THEME VARIANTS
        savannah: "bg-gradient-to-r from-savannah-500 via-sunset-500 to-savannah-600 text-white font-semibold shadow-lg hover:shadow-xl hover:scale-105 hover:from-savannah-600 hover:to-sunset-600",
        wildlife: "bg-gradient-to-r from-wildlife-lion to-wildlife-cheetah text-white font-bold shadow-xl hover:shadow-2xl hover:scale-110 hover:from-wildlife-leopard hover:to-wildlife-lion",
        acacia: "bg-gradient-to-r from-acacia-600 to-baobab-600 text-white font-medium border-2 border-acacia-700 hover:border-acacia-800 shadow-md hover:shadow-lg hover:scale-105",
        sunset: "bg-gradient-to-r from-sunset-500 to-sunset-600 text-white font-semibold shadow-lg hover:shadow-xl hover:scale-105 hover:from-sunset-600 hover:to-sunset-700",
        "savannah-outline": "border-2 border-savannah-500 text-savannah-700 hover:bg-savannah-50 hover:border-savannah-600 hover:text-savannah-800 hover:scale-105",
        "wildlife-ghost": "text-wildlife-lion hover:bg-savannah-100 hover:text-wildlife-cheetah hover:scale-105",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        xl: "h-14 rounded-xl px-10 text-lg font-bold",
        icon: "h-10 w-10",
        "icon-lg": "h-12 w-12",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
