
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, MapPin, Calendar, Users } from 'lucide-react';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      image: 'photo-1472396961693-142e6e269027',
      title: 'Experience the Magic of Tanzania Safari',
      subtitle: 'Witness the Great Migration and encounter Africa\'s Big Five in their natural habitat',
    },
    {
      image: 'photo-1466721591366-2d5fba72006d',
      title: 'Luxury Safari Adventures Await',
      subtitle: 'Premium accommodations and expert guides for an unforgettable journey',
    },
    {
      image: 'photo-1493962853295-0fd70327578a',
      title: 'Cultural Immersion & Wildlife',
      subtitle: 'Connect with local communities while exploring pristine wilderness',
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [slides.length]);

  return (
    <section className="relative h-screen min-h-[600px] overflow-hidden">
      {/* Background Slider */}
      <div className="absolute inset-0">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={`https://images.unsplash.com/${slide.image}?auto=format&fit=crop&w=1920&h=1080`}
              alt={slide.title}
              className="h-full w-full object-cover"
            />
            <div className="absolute inset-0 bg-black/40" />
          </div>
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 flex h-full items-center justify-center">
        <div className="container mx-auto px-4 text-center text-white">
          <h1 className="mb-6 text-4xl md:text-6xl font-bold leading-tight">
            {slides[currentSlide].title}
          </h1>
          <p className="mb-8 text-lg md:text-xl max-w-2xl mx-auto opacity-90">
            {slides[currentSlide].subtitle}
          </p>

          {/* Quick Search Form */}
          <div className="max-w-4xl mx-auto bg-white/95 backdrop-blur rounded-2xl p-6 shadow-2xl">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Where to go?"
                  className="pl-10 h-12 text-gray-900 border-gray-200 focus:border-orange-500"
                />
              </div>
              
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <Input
                  type="date"
                  className="pl-10 h-12 text-gray-900 border-gray-200 focus:border-orange-500"
                />
              </div>

              <div className="relative">
                <Users className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <select className="w-full h-12 pl-10 pr-4 text-gray-900 border border-gray-200 rounded-md focus:border-orange-500 focus:outline-none focus:ring-2 focus:ring-orange-200">
                  <option>2 Travelers</option>
                  <option>1 Traveler</option>
                  <option>3-4 Travelers</option>
                  <option>5+ Travelers</option>
                </select>
              </div>

              <Button className="h-12 bg-orange-600 hover:bg-orange-700 text-white font-semibold">
                <Search className="mr-2 h-5 w-5" />
                Search Tours
              </Button>
            </div>

            <div className="flex flex-wrap gap-2 justify-center">
              <span className="text-sm text-gray-600">Popular:</span>
              {['Serengeti Safari', 'Kilimanjaro Trek', 'Ngorongoro Crater', 'Cultural Tours'].map((tag) => (
                <button
                  key={tag}
                  className="px-3 py-1 bg-gray-100 hover:bg-orange-100 text-gray-700 text-sm rounded-full transition-colors"
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>

          <div className="mt-8 flex justify-center space-x-4">
            <Button size="lg" className="bg-orange-600 hover:bg-orange-700 text-white">
              Explore All Tours
            </Button>
            <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-gray-900">
              Plan Custom Trip
            </Button>
          </div>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`h-2 w-8 rounded-full transition-all ${
              index === currentSlide ? 'bg-white' : 'bg-white/50'
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroSection;
