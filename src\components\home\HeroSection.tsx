
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, MapPin, Calendar, Users } from 'lucide-react';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      image: 'photo-1472396961693-142e6e269027',
      title: 'Experience the Magic of Tanzania Safari',
      subtitle: 'Witness the Great Migration and encounter Africa\'s Big Five in their natural habitat',
    },
    {
      image: 'photo-1466721591366-2d5fba72006d',
      title: 'Luxury Safari Adventures Await',
      subtitle: 'Premium accommodations and expert guides for an unforgettable journey',
    },
    {
      image: 'photo-1493962853295-0fd70327578a',
      title: 'Cultural Immersion & Wildlife',
      subtitle: 'Connect with local communities while exploring pristine wilderness',
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, [slides.length]);

  return (
    <section className="relative h-screen min-h-[600px] overflow-hidden bg-horizon-gradient">
      {/* Enhanced Background Slider with Savannah Effects */}
      <div className="absolute inset-0">
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-all duration-1500 ${
              index === currentSlide ? 'opacity-100 scale-100' : 'opacity-0 scale-105'
            }`}
          >
            <img
              src={`https://images.unsplash.com/${slide.image}?auto=format&fit=crop&w=1920&h=1080`}
              alt={slide.title}
              className="h-full w-full object-cover animate-savannah-breathe"
            />
            {/* Enhanced gradient overlay with savannah colors */}
            <div className="absolute inset-0 bg-gradient-to-b from-savannah-900/30 via-transparent to-sunset-900/50" />
            {/* Subtle texture overlay */}
            <div className="absolute inset-0 bg-savannah-texture opacity-20" />
          </div>
        ))}

        {/* Animated savannah elements */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-savannah-800/20 to-transparent">
          {/* Animated grass silhouettes */}
          <div className="absolute bottom-0 left-1/4 w-2 h-16 bg-savannah-700/30 rounded-t-full animate-grass-sway"></div>
          <div className="absolute bottom-0 left-1/3 w-1 h-12 bg-savannah-600/40 rounded-t-full animate-grass-sway-delayed"></div>
          <div className="absolute bottom-0 right-1/4 w-2 h-20 bg-savannah-700/30 rounded-t-full animate-grass-sway"></div>
          <div className="absolute bottom-0 right-1/3 w-1 h-14 bg-savannah-600/40 rounded-t-full animate-grass-sway-delayed"></div>
        </div>

        {/* Floating dust particles */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-savannah-300/60 rounded-full animate-dust-particle"></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-sunset-300/60 rounded-full animate-dust-particle" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-1/2 w-1 h-1 bg-savannah-400/60 rounded-full animate-dust-particle" style={{animationDelay: '2s'}}></div>
        </div>
      </div>

      {/* Enhanced Content with Wildlife Theme */}
      <div className="relative z-10 flex h-full items-center justify-center">
        <div className="container mx-auto px-4 text-center text-white">
          {/* Animated wildlife icons */}
          <div className="flex justify-center space-x-8 mb-6 opacity-80">
            <div className="text-2xl animate-wildlife-emerge" style={{animationDelay: '0.2s'}}>🦁</div>
            <div className="text-2xl animate-wildlife-emerge" style={{animationDelay: '0.4s'}}>🐘</div>
            <div className="text-2xl animate-wildlife-emerge" style={{animationDelay: '0.6s'}}>🦒</div>
            <div className="text-2xl animate-wildlife-emerge" style={{animationDelay: '0.8s'}}>🦓</div>
            <div className="text-2xl animate-wildlife-emerge" style={{animationDelay: '1.0s'}}>🐆</div>
          </div>

          <h1 className="mb-6 text-4xl md:text-6xl lg:text-7xl font-bold leading-tight font-safari animate-sun-rise">
            <span className="text-savannah-100">{slides[currentSlide].title.split(' ').slice(0, -2).join(' ')}</span>
            <span className="text-wildlife-gold bg-gradient-to-r from-wildlife-lion via-sunset-400 to-wildlife-cheetah bg-clip-text text-transparent">
              {' ' + slides[currentSlide].title.split(' ').slice(-2).join(' ')}
            </span>
          </h1>

          <p className="mb-8 text-lg md:text-xl lg:text-2xl max-w-3xl mx-auto opacity-90 font-adventure leading-relaxed animate-fade-in">
            {slides[currentSlide].subtitle}
          </p>

          {/* Decorative savannah divider */}
          <div className="flex justify-center items-center space-x-4 mb-8 opacity-70">
            <div className="w-16 h-px bg-gradient-to-r from-transparent to-savannah-300"></div>
            <div className="text-savannah-300 text-xl">🌅</div>
            <div className="w-16 h-px bg-gradient-to-l from-transparent to-savannah-300"></div>
          </div>

          {/* Enhanced Quick Search Form with Savannah Theme */}
          <div className="max-w-4xl mx-auto card-savannah backdrop-blur-xl rounded-3xl p-6 md:p-8 shadow-2xl border-2 border-savannah-200/50 animate-scale-in">
            {/* Search form header */}
            <div className="text-center mb-6">
              <h3 className="text-savannah-800 font-bold text-lg md:text-xl font-safari">🔍 Find Your Perfect Safari</h3>
              <p className="text-savannah-600 text-sm font-adventure">Discover the wild beauty of Tanzania</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="relative group">
                <MapPin className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-savannah-500 group-hover:text-sunset-500 transition-colors" />
                <Input
                  placeholder="🌍 Where to go?"
                  className="pl-10 h-12 text-savannah-900 border-savannah-300 focus:border-sunset-500 focus:ring-sunset-200 rounded-xl bg-white/90 hover:bg-white transition-all font-adventure"
                />
              </div>

              <div className="relative group">
                <Calendar className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-savannah-500 group-hover:text-sunset-500 transition-colors" />
                <Input
                  type="date"
                  className="pl-10 h-12 text-savannah-900 border-savannah-300 focus:border-sunset-500 focus:ring-sunset-200 rounded-xl bg-white/90 hover:bg-white transition-all font-adventure"
                />
              </div>

              <div className="relative group">
                <Users className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-savannah-500 group-hover:text-sunset-500 transition-colors" />
                <select className="w-full h-12 pl-10 pr-4 text-savannah-900 border border-savannah-300 rounded-xl focus:border-sunset-500 focus:outline-none focus:ring-2 focus:ring-sunset-200 bg-white/90 hover:bg-white transition-all font-adventure">
                  <option>👥 2 Travelers</option>
                  <option>🧑 1 Traveler</option>
                  <option>👨‍👩‍👧‍👦 3-4 Travelers</option>
                  <option>👥 5+ Travelers</option>
                </select>
              </div>

              <Button className="btn-wildlife h-12 font-bold text-base group relative overflow-hidden">
                <div className="absolute inset-0 bg-wildlife-pattern opacity-10 group-hover:opacity-20 transition-opacity"></div>
                <Search className="mr-2 h-5 w-5 group-hover:animate-animal-walk" />
                <span className="relative z-10">🔍 Search Tours</span>
              </Button>
            </div>

            {/* Enhanced Popular Tags */}
            <div className="flex flex-wrap gap-3 justify-center items-center">
              <span className="text-sm text-savannah-700 font-semibold font-adventure">🔥 Popular Adventures:</span>
              {[
                { name: 'Serengeti Safari', icon: '🦁' },
                { name: 'Kilimanjaro Trek', icon: '🏔️' },
                { name: 'Ngorongoro Crater', icon: '🌋' },
                { name: 'Cultural Tours', icon: '🏛️' }
              ].map((tag, index) => (
                <button
                  key={tag.name}
                  className="px-4 py-2 bg-gradient-to-r from-savannah-100 to-acacia-100 hover:from-sunset-100 hover:to-savannah-200 text-savannah-800 text-sm rounded-full transition-all duration-300 hover:scale-105 hover:shadow-md border border-savannah-200 font-adventure animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <span className="mr-1">{tag.icon}</span>
                  {tag.name}
                </button>
              ))}
            </div>
          </div>

          {/* Enhanced Action Buttons */}
          <div className="mt-10 flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-6">
            <Button size="lg" className="btn-wildlife px-8 py-4 text-lg font-bold group relative overflow-hidden">
              <div className="absolute inset-0 bg-wildlife-pattern opacity-10 group-hover:opacity-20 transition-opacity"></div>
              <span className="relative z-10 flex items-center">
                🌍 Explore All Tours
              </span>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
            </Button>

            <Button size="lg" variant="outline" className="px-8 py-4 text-lg font-bold text-white border-2 border-savannah-300 hover:bg-savannah-100/20 hover:border-sunset-300 hover:text-savannah-100 transition-all duration-300 rounded-xl backdrop-blur-sm group">
              <span className="flex items-center">
                ✨ Plan Custom Trip
              </span>
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Slide Indicators with Wildlife Theme */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-3 bg-savannah-900/20 backdrop-blur-sm rounded-full px-4 py-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`h-3 w-10 rounded-full transition-all duration-300 hover:scale-110 ${
              index === currentSlide
                ? 'bg-gradient-to-r from-wildlife-lion to-sunset-500 shadow-lg'
                : 'bg-savannah-300/60 hover:bg-savannah-200/80'
            }`}
          />
        ))}
      </div>

      {/* Floating wildlife silhouettes for ambiance */}
      <div className="absolute bottom-20 right-10 opacity-30 animate-wildlife-emerge hidden lg:block">
        <div className="text-4xl text-savannah-200">🦒</div>
      </div>
      <div className="absolute bottom-32 left-10 opacity-20 animate-wildlife-emerge hidden lg:block" style={{animationDelay: '1s'}}>
        <div className="text-3xl text-savannah-300">🌳</div>
      </div>
    </section>
  );
};

export default HeroSection;
