
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: {
				DEFAULT: '1rem',
				sm: '1.5rem',
				lg: '2rem',
				xl: '2rem',
				'2xl': '2rem',
			},
			screens: {
				'sm': '640px',
				'md': '768px',
				'lg': '1024px',
				'xl': '1280px',
				'2xl': '1400px'
			}
		},
		screens: {
			'xs': '475px',
			'sm': '640px',
			'md': '768px',
			'lg': '1024px',
			'xl': '1280px',
			'2xl': '1536px',
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// 🦁 SAVANNAH WILDLIFE THEME COLORS
				savannah: {
					50: '#fefdf8',   // Lightest cream
					100: '#fdf9e7',  // Pale sand
					200: '#faf0c4',  // Light sand
					300: '#f5e197',  // Golden sand
					400: '#edc75f',  // Warm gold
					500: '#e4a853',  // Rich gold
					600: '#d4924a',  // Deep gold
					700: '#b8763e',  // Bronze
					800: '#9a5f37',  // Dark bronze
					900: '#7d4d2f',  // Deep earth
					950: '#432818'   // Darkest earth
				},
				acacia: {
					50: '#f8f6f0',   // Light bark
					100: '#ede8d8',  // Pale bark
					200: '#ddd2b4',  // Light wood
					300: '#c8b588',  // Medium wood
					400: '#b59a64',  // Rich wood
					500: '#a6884f',  // Deep wood
					600: '#8f7043',  // Dark wood
					700: '#755a39',  // Darker wood
					800: '#614a32',  // Deep bark
					900: '#533f2d',  // Darkest bark
					950: '#2e2117'   // Almost black bark
				},
				sunset: {
					50: '#fef7ed',   // Lightest peach
					100: '#fdedd3',  // Pale sunset
					200: '#fad7a5',  // Light orange
					300: '#f6ba6d',  // Warm orange
					400: '#f19332',  // Bright orange
					500: '#ed7014',  // Deep orange
					600: '#de5409',  // Burnt orange
					700: '#b8400a',  // Dark burnt orange
					800: '#94330f',  // Deep burnt
					900: '#792c10',  // Darkest burnt
					950: '#411406'   // Almost black burnt
				},
				baobab: {
					50: '#f6f6f4',   // Lightest gray-green
					100: '#e8e8e3',  // Pale sage
					200: '#d3d3c8',  // Light sage
					300: '#b8b8a6',  // Medium sage
					400: '#9d9d84',  // Rich sage
					500: '#8a8a6f',  // Deep sage
					600: '#6f6f5c',  // Dark sage
					700: '#5a5a4c',  // Darker sage
					800: '#4a4a40',  // Deep gray-green
					900: '#3f3f37',  // Darkest sage
					950: '#22221d'   // Almost black sage
				},
				wildlife: {
					lion: '#d4924a',      // Lion mane gold
					elephant: '#8a8a6f',  // Elephant gray
					zebra: '#2d2d2d',     // Zebra black
					giraffe: '#f5e197',   // Giraffe spots
					leopard: '#edc75f',   // Leopard gold
					rhino: '#6f6f5c',     // Rhino gray
					cheetah: '#f19332',   // Cheetah orange
					buffalo: '#432818'    // Buffalo dark
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			fontSize: {
				'2xs': ['0.625rem', { lineHeight: '0.75rem' }],
			},
			fontFamily: {
				'safari': ['Merriweather', 'Georgia', 'serif'],
				'adventure': ['Montserrat', 'sans-serif'],
				'nature': ['Playfair Display', 'serif'],
			},
			backgroundImage: {
				'savannah-grass': "url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23f5e197\" fill-opacity=\"0.1\"%3E%3Cpath d=\"M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm15 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')",
				'acacia-pattern': "url('data:image/svg+xml,%3Csvg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23c8b588\" fill-opacity=\"0.08\" fill-rule=\"evenodd\"%3E%3Cpath d=\"M0 40L40 0H20L0 20M40 40V20L20 40\"/%3E%3C/g%3E%3C/svg%3E')",
				'sunset-gradient': 'linear-gradient(135deg, #fef7ed 0%, #fdedd3 25%, #f6ba6d 50%, #f19332 75%, #ed7014 100%)',
				'savannah-horizon': 'linear-gradient(180deg, #f5e197 0%, #edc75f 30%, #d4924a 60%, #b8763e 100%)',
				'wildlife-texture': "url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23d4924a\" fill-opacity=\"0.05\" fill-rule=\"evenodd\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"3\"/%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"3\"/%3E%3C/g%3E%3C/svg%3E')",
			},
			spacing: {
				'safe-top': 'env(safe-area-inset-top)',
				'safe-bottom': 'env(safe-area-inset-bottom)',
				'safe-left': 'env(safe-area-inset-left)',
				'safe-right': 'env(safe-area-inset-right)',
			},
			minHeight: {
				'screen-safe': 'calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom))',
			},
			maxWidth: {
				'8xl': '88rem',
				'9xl': '96rem',
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					'0%': {
						opacity: '0',
						transform: 'translateY(10px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'slide-in-right': {
					'0%': {
						transform: 'translateX(100%)'
					},
					'100%': {
						transform: 'translateX(0)'
					}
				},
				'slide-in-left': {
					'0%': {
						transform: 'translateX(-100%)'
					},
					'100%': {
						transform: 'translateX(0)'
					}
				},
				'slide-in-up': {
					'0%': {
						transform: 'translateY(100%)',
						opacity: '0'
					},
					'100%': {
						transform: 'translateY(0)',
						opacity: '1'
					}
				},
				'scale-in': {
					'0%': {
						transform: 'scale(0.9)',
						opacity: '0'
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '1'
					}
				},
				'bounce-in': {
					'0%': {
						transform: 'scale(0.3)',
						opacity: '0'
					},
					'50%': {
						transform: 'scale(1.05)'
					},
					'70%': {
						transform: 'scale(0.9)'
					},
					'100%': {
						transform: 'scale(1)',
						opacity: '1'
					}
				},
				// 🌾 SAVANNAH-INSPIRED ANIMATIONS
				'grass-sway': {
					'0%, 100%': {
						transform: 'rotate(-2deg) translateX(0px)'
					},
					'50%': {
						transform: 'rotate(2deg) translateX(2px)'
					}
				},
				'animal-walk': {
					'0%': {
						transform: 'translateX(-10px)'
					},
					'50%': {
						transform: 'translateX(5px) translateY(-2px)'
					},
					'100%': {
						transform: 'translateX(0px)'
					}
				},
				'sun-rise': {
					'0%': {
						transform: 'translateY(20px) scale(0.8)',
						opacity: '0.6'
					},
					'100%': {
						transform: 'translateY(0px) scale(1)',
						opacity: '1'
					}
				},
				'horizon-glow': {
					'0%, 100%': {
						background: 'linear-gradient(135deg, #fef7ed 0%, #fdedd3 100%)'
					},
					'50%': {
						background: 'linear-gradient(135deg, #f6ba6d 0%, #f19332 100%)'
					}
				},
				'wildlife-emerge': {
					'0%': {
						transform: 'scale(0.8) translateY(20px)',
						opacity: '0'
					},
					'60%': {
						transform: 'scale(1.05) translateY(-5px)',
						opacity: '0.8'
					},
					'100%': {
						transform: 'scale(1) translateY(0px)',
						opacity: '1'
					}
				},
				'acacia-grow': {
					'0%': {
						transform: 'scaleY(0.3) scaleX(0.8)',
						transformOrigin: 'bottom'
					},
					'100%': {
						transform: 'scaleY(1) scaleX(1)',
						transformOrigin: 'bottom'
					}
				},
				'dust-particle': {
					'0%': {
						transform: 'translateY(0px) translateX(0px) rotate(0deg)',
						opacity: '0'
					},
					'50%': {
						transform: 'translateY(-10px) translateX(5px) rotate(180deg)',
						opacity: '0.6'
					},
					'100%': {
						transform: 'translateY(-20px) translateX(10px) rotate(360deg)',
						opacity: '0'
					}
				},
				'savannah-breathe': {
					'0%, 100%': {
						transform: 'scale(1)'
					},
					'50%': {
						transform: 'scale(1.02)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.5s ease-out',
				'slide-in-right': 'slide-in-right 0.3s ease-out',
				'slide-in-left': 'slide-in-left 0.3s ease-out',
				'slide-in-up': 'slide-in-up 0.4s ease-out',
				'scale-in': 'scale-in 0.3s ease-out',
				'bounce-in': 'bounce-in 0.6s ease-out',
				// 🌾 SAVANNAH ANIMATIONS
				'grass-sway': 'grass-sway 3s ease-in-out infinite',
				'grass-sway-delayed': 'grass-sway 3s ease-in-out infinite 0.5s',
				'animal-walk': 'animal-walk 2s ease-in-out',
				'sun-rise': 'sun-rise 1.5s ease-out',
				'horizon-glow': 'horizon-glow 8s ease-in-out infinite',
				'wildlife-emerge': 'wildlife-emerge 1.2s ease-out',
				'acacia-grow': 'acacia-grow 2s ease-out',
				'dust-particle': 'dust-particle 4s linear infinite',
				'savannah-breathe': 'savannah-breathe 4s ease-in-out infinite',
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
