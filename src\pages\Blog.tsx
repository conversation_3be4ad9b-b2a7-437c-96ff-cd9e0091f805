import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import { Calendar, User, Search, ArrowRight, Clock, Eye, Heart, MessageCircle } from 'lucide-react';

const Blog = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const blogPosts = [
    {
      id: 1,
      title: "The Ultimate Guide to Tanzania Safari: Best Parks and When to Visit",
      excerpt: "Discover the magnificent wildlife parks of Tanzania and learn the best times to witness the Great Migration, stunning landscapes, and incredible wildlife encounters.",
      content: "Tanzania offers some of the world's most spectacular safari experiences...",
      author: "<PERSON>",
      authorAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=150&h=150",
      date: "2024-02-15",
      category: "safari-guides",
      featured: true,
      image: "https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=800&h=500",
      readTime: "8 min read",
      views: 2340,
      likes: 156,
      comments: 23
    },
    {
      id: 2,
      title: "Conquering Kilimanjaro: A Complete Preparation Guide",
      excerpt: "Everything you need to know about climbing Africa's highest peak, from training tips to packing essentials and route selection.",
      content: "Mount Kilimanjaro stands as Africa's highest peak...",
      author: "David Mwasumbi",
      authorAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&h=150",
      date: "2024-02-10",
      category: "kilimanjaro",
      featured: false,
      image: "https://images.unsplash.com/photo-1589182373726-e4f658ab50f2?auto=format&fit=crop&w=800&h=500",
      readTime: "12 min read",
      views: 1890,
      likes: 134,
      comments: 45
    },
    {
      id: 3,
      title: "Wildlife Photography Tips: Capturing the Perfect Safari Shot",
      excerpt: "Professional photographer shares insider tips for taking stunning wildlife photos during your Tanzania safari adventure.",
      content: "Wildlife photography requires patience, skill, and the right equipment...",
      author: "Emily Waters",
      authorAvatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=150&h=150",
      date: "2024-02-05",
      category: "photography",
      featured: true,
      image: "https://images.unsplash.com/photo-1564760055775-d63b17a55c44?auto=format&fit=crop&w=800&h=500",
      readTime: "6 min read",
      views: 1560,
      likes: 98,
      comments: 18
    },
    {
      id: 4,
      title: "Sustainable Safari: How to Travel Responsibly in Tanzania",
      excerpt: "Learn about eco-friendly safari practices and how your visit contributes to wildlife conservation and local communities.",
      content: "Sustainable tourism is crucial for preserving Tanzania's natural heritage...",
      author: "Dr. Michael Kimaro",
      authorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=150&h=150",
      date: "2024-01-28",
      category: "conservation",
      featured: false,
      image: "https://images.unsplash.com/photo-1547036967-23d11aacaee0?auto=format&fit=crop&w=800&h=500",
      readTime: "10 min read",
      views: 987,
      likes: 67,
      comments: 12
    },
    {
      id: 5,
      title: "Maasai Culture: Understanding Tanzania's Indigenous Heritage",
      excerpt: "Dive deep into the rich traditions, customs, and modern life of the Maasai people in northern Tanzania.",
      content: "The Maasai people have maintained their cultural identity for centuries...",
      author: "Grace Mollel",
      authorAvatar: "https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?auto=format&fit=crop&w=150&h=150",
      date: "2024-01-20",
      category: "culture",
      featured: false,
      image: "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&h=500",
      readTime: "7 min read",
      views: 1234,
      likes: 89,
      comments: 16
    },
    {
      id: 6,
      title: "Best Time to Visit Tanzania: Seasonal Wildlife Guide",
      excerpt: "Navigate Tanzania's seasons to maximize your wildlife viewing opportunities and avoid the crowds.",
      content: "Understanding Tanzania's seasons is key to planning the perfect safari...",
      author: "James Wilson",
      authorAvatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=150&h=150",
      date: "2024-01-15",
      category: "travel-tips",
      featured: false,
      image: "https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=800&h=500",
      readTime: "9 min read",
      views: 2100,
      likes: 145,
      comments: 28
    }
  ];

  const categories = [
    { value: 'all', label: 'All Posts' },
    { value: 'safari-guides', label: 'Safari Guides' },
    { value: 'kilimanjaro', label: 'Kilimanjaro' },
    { value: 'photography', label: 'Photography' },
    { value: 'conservation', label: 'Conservation' },
    { value: 'culture', label: 'Culture' },
    { value: 'travel-tips', label: 'Travel Tips' }
  ];

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const featuredPosts = blogPosts.filter(post => post.featured);

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <div className="relative h-96 overflow-hidden">
          <div 
            className="absolute inset-0 w-full h-full"
            style={{
              backgroundImage: 'url(https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=1920&h=1080)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-4xl mx-auto">
              <Badge className="mb-4 bg-orange-600 text-white px-4 py-2">
                <Calendar className="w-4 h-4 mr-2" />
                Safari Stories & Guides
              </Badge>
              <h1 className="text-5xl md:text-6xl font-bold mb-6">Safari Blog</h1>
              <p className="text-xl md:text-2xl leading-relaxed opacity-90">
                Discover insider tips, wildlife stories, and expert guides for your Tanzania adventure
              </p>
            </div>
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white border-b border-gray-200 sticky top-20 z-40">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Button
                    key={category.value}
                    variant={selectedCategory === category.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.value)}
                    className={selectedCategory === category.value ? "bg-orange-600 hover:bg-orange-700" : "hover:bg-orange-50"}
                  >
                    {category.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Featured Posts Section */}
        {selectedCategory === 'all' && (
          <div className="container mx-auto px-4 py-12">
            <h2 className="text-3xl font-bold mb-8">Featured Articles</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {featuredPosts.slice(0, 2).map((post, index) => (
                <Link key={post.id} to={`/blog/${post.id}`}>
                  <Card className="group overflow-hidden hover:shadow-2xl transition-all duration-300 cursor-pointer">
                    <div className="relative overflow-hidden">
                      <img
                        src={post.image}
                        alt={post.title}
                        className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                      <Badge className="absolute top-4 left-4 bg-orange-600 text-white">
                        Featured
                      </Badge>
                    </div>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {post.author}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {new Date(post.date).toLocaleDateString()}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {post.readTime}
                        </div>
                      </div>
                      <h3 className="text-xl font-bold mb-3 group-hover:text-orange-600 transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-gray-600 mb-4 line-clamp-3">{post.excerpt}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            {post.views.toLocaleString()}
                          </div>
                          <div className="flex items-center gap-1">
                            <Heart className="h-4 w-4" />
                            {post.likes}
                          </div>
                          <div className="flex items-center gap-1">
                            <MessageCircle className="h-4 w-4" />
                            {post.comments}
                          </div>
                        </div>
                        <Button variant="ghost" className="text-orange-600 hover:text-orange-700">
                          Read More
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* All Posts Grid */}
        <div className="container mx-auto px-4 py-12">
          <h2 className="text-3xl font-bold mb-8">
            {selectedCategory === 'all' ? 'All Articles' : `${categories.find(c => c.value === selectedCategory)?.label} Articles`}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post) => (
              <Link key={post.id} to={`/blog/${post.id}`}>
                <Card className="group overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 cursor-pointer">
                  <div className="relative overflow-hidden">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                    <Badge className="absolute top-3 left-3 bg-orange-600 text-white text-xs">
                      {categories.find(c => c.value === post.category)?.label}
                    </Badge>
                  </div>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Avatar className="h-8 w-8">
                        <img src={post.authorAvatar} alt={post.author} className="object-cover" />
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{post.author}</p>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <Calendar className="h-3 w-3" />
                          {new Date(post.date).toLocaleDateString()}
                          <Clock className="h-3 w-3 ml-2" />
                          {post.readTime}
                        </div>
                      </div>
                    </div>
                    <h3 className="text-lg font-bold mb-2 group-hover:text-orange-600 transition-colors line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">{post.excerpt}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {post.views.toLocaleString()}
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          {post.likes}
                        </div>
                      </div>
                      <Button size="sm" variant="ghost" className="text-orange-600 hover:text-orange-700 p-0">
                        Read More
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="bg-gradient-to-r from-orange-600 to-orange-700 py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Stay Updated with Safari Stories</h2>
            <p className="text-xl text-orange-100 mb-8 max-w-2xl mx-auto">
              Get the latest safari tips, wildlife updates, and exclusive travel guides delivered to your inbox
            </p>
            <div className="flex flex-col md:flex-row gap-4 max-w-md mx-auto">
              <Input
                placeholder="Enter your email"
                className="bg-white/90 border-white/50 placeholder:text-gray-600"
              />
              <Button className="bg-white text-orange-600 hover:bg-gray-100 font-semibold">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Blog;
