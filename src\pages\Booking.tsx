
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import EnhancedBookingForm from '@/components/booking/EnhancedBookingForm';
import BookingSummary from '@/components/booking/BookingSummary';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Clock, Phone, Star, MapPin } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface BookingData {
  tourId: string;
  startDate: Date | null;
  groupSize: number;
  travelers: Array<{
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth: Date | null;
    passportNumber: string;
    nationality: string;
  }>;
  accommodation: string;
  addOns: string[];
  specialRequests: string;
  totalPrice: number;
}

const Booking = () => {
  const { id } = useParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [customTour, setCustomTour] = useState<any>(null);
  const [bookingData, setBookingData] = useState<BookingData>({
    tourId: id || '',
    startDate: null,
    groupSize: 2,
    travelers: [],
    accommodation: '',
    addOns: [],
    specialRequests: '',
    totalPrice: 0
  });

  // Check for custom tour data from tour builder
  useEffect(() => {
    if (id?.startsWith('custom-')) {
      const storedTour = sessionStorage.getItem('customTour');
      if (storedTour) {
        const tour = JSON.parse(storedTour);
        setCustomTour(tour);
        setBookingData(prev => ({
          ...prev,
          totalPrice: tour.price,
          specialRequests: tour.customData?.builderData?.specialRequests || '',
          accommodation: tour.customData?.builderData?.accommodation || ''
        }));
      }
    }
  }, [id]);

  const steps = [
    { id: 1, title: 'Tour Selection', description: 'Choose dates and packages' },
    { id: 2, title: 'Traveler Information', description: 'Add traveler details' },
    { id: 3, title: 'Customize Experience', description: 'Select preferences and add-ons' },
    { id: 4, title: 'Payment & Confirmation', description: 'Complete your booking' }
  ];

  const updateBookingData = (field: keyof BookingData, value: any) => {
    setBookingData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {customTour ? 'Book Your Custom Safari' : 'Book Your Safari Adventure'}
            </h1>
            <p className="text-xl max-w-2xl">
              {customTour 
                ? 'Complete your custom safari booking and get ready for your personalized adventure'
                : 'Complete your booking in just a few simple steps and get ready for the adventure of a lifetime'
              }
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
            {/* Custom Tour Summary (if applicable) */}
            {customTour && (
              <Card className="mb-8 border-orange-200 bg-orange-50">
                <CardHeader>
                  <CardTitle className="flex items-center text-orange-800">
                    <Star className="mr-2 h-5 w-5" />
                    Your Custom Safari Tour
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <h4 className="font-semibold text-orange-800 mb-2">Destinations</h4>
                      <div className="flex flex-wrap gap-1">
                        {customTour.destinations?.map((dest: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-orange-700 border-orange-300">
                            {dest}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-orange-800 mb-2">Duration & Price</h4>
                      <p className="text-orange-700">{customTour.duration} • ${customTour.price}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-orange-800 mb-2">Activities</h4>
                      <p className="text-orange-700">{customTour.activities?.slice(0, 2).join(', ')}{customTour.activities?.length > 2 ? '...' : ''}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-orange-700">
                    <MapPin className="h-4 w-4" />
                    <span>Custom itinerary created just for you</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Enhanced Progress Steps */}
            <div className="flex justify-center mb-8">
              <div className="flex items-center space-x-4 overflow-x-auto pb-4">
                {steps.map((step, index) => (
                  <React.Fragment key={step.id}>
                    <div className="flex items-center min-w-fit">
                      <div 
                        className={`w-12 h-12 rounded-full flex items-center justify-center text-sm font-semibold transition-all ${
                          currentStep >= step.id 
                            ? 'bg-orange-600 text-white shadow-lg' 
                            : 'bg-gray-200 text-gray-600'
                        }`}
                      >
                        {step.id}
                      </div>
                      <div className="ml-3 hidden md:block">
                        <div className={`text-sm font-medium transition-colors ${
                          currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'
                        }`}>
                          {step.title}
                        </div>
                        <div className="text-xs text-gray-500">{step.description}</div>
                      </div>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-16 h-0.5 transition-colors ${
                        currentStep > step.id ? 'bg-orange-600' : 'bg-gray-200'
                      }`} />
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>

            {/* Current Step Indicator for Mobile */}
            <div className="md:hidden text-center mb-6">
              <h2 className="text-xl font-bold text-gray-900">{steps[currentStep - 1].title}</h2>
              <p className="text-gray-600">{steps[currentStep - 1].description}</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Enhanced Booking Form */}
              <div className="lg:col-span-2">
                <EnhancedBookingForm
                  tourId={id || ''}
                  currentStep={currentStep}
                  bookingData={bookingData}
                  onDataChange={updateBookingData}
                  onNext={nextStep}
                  onPrev={prevStep}
                  customTour={customTour}
                />
              </div>

              {/* Enhanced Sidebar */}
              <div className="lg:col-span-1">
                <div className="space-y-6 sticky top-4">
                  <BookingSummary bookingData={bookingData} customTour={customTour} />
                  
                  {/* Enhanced Trust Indicators */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Why Book With Us?</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <Shield className="h-5 w-5 text-green-600 mt-1" />
                        <div>
                          <div className="font-semibold text-sm">Secure Booking</div>
                          <div className="text-xs text-gray-600">SSL encrypted & GDPR compliant</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <Clock className="h-5 w-5 text-blue-600 mt-1" />
                        <div>
                          <div className="font-semibold text-sm">Flexible Cancellation</div>
                          <div className="text-xs text-gray-600">Free cancellation up to 48 hours</div>
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <Phone className="h-5 w-5 text-orange-600 mt-1" />
                        <div>
                          <div className="font-semibold text-sm">24/7 Expert Support</div>
                          <div className="text-xs text-gray-600">Safari specialists always available</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Custom Tour Benefits */}
                  {customTour && (
                    <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
                      <CardHeader>
                        <CardTitle className="text-lg text-green-800">Custom Tour Benefits</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3 text-sm">
                          <div>
                            <div className="font-semibold text-green-800">Personalized Experience</div>
                            <div className="text-green-700">Tailored to your preferences</div>
                          </div>
                          <div>
                            <div className="font-semibold text-green-800">Flexible Itinerary</div>
                            <div className="text-green-700">Can be adjusted before departure</div>
                          </div>
                          <div>
                            <div className="font-semibold text-green-800">Expert Guidance</div>
                            <div className="text-green-700">Dedicated safari specialist</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Seasonal Information */}
                  <Card className="bg-gradient-to-br from-orange-50 to-yellow-50 border-orange-200">
                    <CardHeader>
                      <CardTitle className="text-lg text-orange-800">Seasonal Highlights</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3 text-sm">
                        <div>
                          <div className="font-semibold text-orange-800">Current Season</div>
                          <div className="text-orange-700">Dry Season - Excellent game viewing</div>
                        </div>
                        <div>
                          <div className="font-semibold text-orange-800">Wildlife Activity</div>
                          <div className="text-orange-700">High - Animals around water sources</div>
                        </div>
                        <div>
                          <div className="font-semibold text-orange-800">Photography Conditions</div>
                          <div className="text-orange-700">Excellent - Clear skies and golden light</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Emergency Contact */}
                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="p-4">
                      <div className="text-center">
                        <div className="font-semibold text-blue-900 mb-2">Need Help?</div>
                        <div className="text-sm text-blue-800 mb-3">
                          Our safari experts are here to assist you
                        </div>
                        <div className="space-y-2">
                          <div className="text-sm">
                            <span className="font-medium">Phone:</span> +255 123 456 789
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">WhatsApp:</span> +255 123 456 789
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Email:</span> <EMAIL>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Booking;
