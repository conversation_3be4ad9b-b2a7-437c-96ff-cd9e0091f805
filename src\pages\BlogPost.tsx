
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Calendar, 
  User, 
  Clock, 
  Eye, 
  Heart, 
  MessageCircle, 
  Share, 
  ArrowLeft,
  BookmarkPlus,
  Facebook,
  Twitter,
  Linkedin
} from 'lucide-react';

const BlogPost = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [commentText, setCommentText] = useState('');
  const [comments, setComments] = useState([]);

  // Mock blog post data (in real app, this would come from database)
  const blogPosts = [
    {
      id: 1,
      title: "The Ultimate Guide to Tanzania Safari: Best Parks and When to Visit",
      content: `
        <p>Tanzania offers some of the world's most spectacular safari experiences, with diverse ecosystems ranging from the endless plains of the Serengeti to the dramatic landscapes of the Ngorongoro Crater. This comprehensive guide will help you plan the perfect safari adventure.</p>
        
        <h2>Best Safari Parks in Tanzania</h2>
        
        <h3>Serengeti National Park</h3>
        <p>The Serengeti is undoubtedly Tanzania's most famous national park, covering 14,750 square kilometers of diverse landscapes. Home to the Great Migration, where over 2 million wildebeest, zebras, and gazelles move in an endless cycle following the rains and fresh grass.</p>
        
        <h3>Ngorongoro Conservation Area</h3>
        <p>Often called the "Eighth Wonder of the World," the Ngorongoro Crater is a massive caldera that contains an incredible concentration of wildlife. The crater floor is home to approximately 25,000 large animals, including the Big Five.</p>
        
        <h3>Tarangire National Park</h3>
        <p>Famous for its large elephant herds and iconic baobab trees, Tarangire offers excellent wildlife viewing, especially during the dry season when animals congregate around the Tarangire River.</p>
        
        <h2>Best Time to Visit</h2>
        
        <h3>Dry Season (June - October)</h3>
        <p>This is the peak safari season with excellent wildlife viewing opportunities. Animals gather around water sources, making them easier to spot. The weather is pleasant with minimal rainfall.</p>
        
        <h3>Wet Season (November - May)</h3>
        <p>The wet season offers lush green landscapes and is the best time for bird watching. It's also calving season for many animals, providing unique wildlife experiences at lower prices.</p>
        
        <h2>Planning Your Safari</h2>
        <p>When planning your Tanzania safari, consider factors such as your budget, preferred accommodation level, time of year, and specific wildlife you want to see. A typical safari lasts 5-7 days, allowing you to visit multiple parks and maximize your wildlife viewing opportunities.</p>
        
        <p>Remember to pack appropriate clothing, including neutral colors, sun protection, and comfortable walking shoes. Don't forget your camera with a good zoom lens to capture those once-in-a-lifetime moments!</p>
      `,
      author: "Sarah Johnson",
      authorAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=150&h=150",
      date: "2024-02-15",
      category: "safari-guides",
      image: "https://images.unsplash.com/photo-1516426122078-c23e76319801?auto=format&fit=crop&w=1200&h=600",
      readTime: "8 min read",
      views: 2340,
      likes: 156,
      comments: 23,
      tags: ["Safari", "Tanzania", "Wildlife", "Travel Guide"]
    }
  ];

  const post = blogPosts.find(p => p.id === parseInt(id || '1')) || blogPosts[0];

  useEffect(() => {
    setLikeCount(post.likes);
  }, [post.likes]);

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
    // In real app, this would update the backend
  };

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked);
    // In real app, this would update the backend
  };

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (commentText.trim()) {
      const newComment = {
        id: Date.now(),
        text: commentText,
        author: "Current User",
        date: new Date().toISOString(),
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=50&h=50"
      };
      setComments([newComment, ...comments]);
      setCommentText('');
      // In real app, this would save to backend
    }
  };

  const shareUrl = window.location.href;

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16 md:pt-20">
        {/* Back Button */}
        <div className="container mx-auto px-4 py-3 md:py-4">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/blog')}
            className="flex items-center gap-2 text-orange-600 hover:text-orange-700 p-2 md:p-3"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm md:text-base">Back to Blog</span>
          </Button>
        </div>

        {/* Hero Image */}
        <div className="relative h-48 md:h-64 lg:h-96 overflow-hidden">
          <img
            src={post.image}
            alt={post.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
        </div>

        {/* Article Content */}
        <div className="container mx-auto px-4 py-6 md:py-12">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
              {/* Main Content */}
              <div className="lg:col-span-2">
                {/* Article Header */}
                <div className="mb-6 md:mb-8">
                  <Badge className="mb-3 md:mb-4 bg-orange-600 text-white text-xs md:text-sm">
                    {post.category.replace('-', ' ')}
                  </Badge>
                  <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6 leading-tight">{post.title}</h1>
                  
                  {/* Author Info */}
                  <div className="flex items-center gap-3 md:gap-4 mb-4 md:mb-6">
                    <Avatar className="h-10 w-10 md:h-12 md:w-12">
                      <img src={post.authorAvatar} alt={post.author} className="object-cover" />
                    </Avatar>
                    <div>
                      <p className="font-semibold text-sm md:text-base">{post.author}</p>
                      <div className="flex flex-wrap items-center gap-2 md:gap-4 text-xs md:text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 md:h-4 md:w-4" />
                          <span className="hidden sm:inline">
                            {new Date(post.date).toLocaleDateString('en-US', { 
                              year: 'numeric', 
                              month: 'long', 
                              day: 'numeric' 
                            })}
                          </span>
                          <span className="sm:hidden">
                            {new Date(post.date).toLocaleDateString('en-US', { 
                              month: 'short', 
                              day: 'numeric' 
                            })}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3 md:h-4 md:w-4" />
                          {post.readTime}
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3 md:h-4 md:w-4" />
                          {post.views.toLocaleString()} views
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Engagement Buttons */}
                  <div className="flex items-center gap-2 md:gap-4 pb-4 md:pb-6 border-b">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLike}
                      className={`flex items-center gap-1 md:gap-2 text-xs md:text-sm px-2 md:px-3 ${isLiked ? 'text-red-600' : 'text-gray-600'}`}
                    >
                      <Heart className={`h-3 w-3 md:h-4 md:w-4 ${isLiked ? 'fill-current' : ''}`} />
                      {likeCount}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleBookmark}
                      className={`flex items-center gap-1 md:gap-2 text-xs md:text-sm px-2 md:px-3 ${isBookmarked ? 'text-orange-600' : 'text-gray-600'}`}
                    >
                      <BookmarkPlus className={`h-3 w-3 md:h-4 md:w-4 ${isBookmarked ? 'fill-current' : ''}`} />
                      <span className="hidden sm:inline">Save</span>
                    </Button>
                    <Button variant="ghost" size="sm" className="flex items-center gap-1 md:gap-2 text-xs md:text-sm px-2 md:px-3 text-gray-600">
                      <Share className="h-3 w-3 md:h-4 md:w-4" />
                      <span className="hidden sm:inline">Share</span>
                    </Button>
                  </div>
                </div>

                {/* Article Body */}
                <div 
                  className="prose prose-sm md:prose-lg max-w-none mb-8 md:mb-12 [&>h2]:text-xl [&>h2]:md:text-2xl [&>h3]:text-lg [&>h3]:md:text-xl [&>p]:text-sm [&>p]:md:text-base [&>p]:leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: post.content }}
                />

                {/* Tags */}
                <div className="mb-6 md:mb-8">
                  <h3 className="font-semibold mb-3 text-sm md:text-base">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-orange-600 border-orange-600 text-xs md:text-sm">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <Separator className="my-6 md:my-8" />

                {/* Comments Section */}
                <div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-6 flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 md:h-6 md:w-6" />
                    Comments ({post.comments + comments.length})
                  </h3>

                  {/* Comment Form */}
                  <form onSubmit={handleCommentSubmit} className="mb-6 md:mb-8">
                    <div className="space-y-3 md:space-y-4">
                      <Textarea
                        placeholder="Share your thoughts..."
                        value={commentText}
                        onChange={(e) => setCommentText(e.target.value)}
                        className="min-h-[80px] md:min-h-[100px] text-sm md:text-base"
                      />
                      <Button type="submit" className="bg-orange-600 hover:bg-orange-700 text-sm md:text-base px-4 md:px-6">
                        Post Comment
                      </Button>
                    </div>
                  </form>

                  {/* Comments List */}
                  <div className="space-y-4 md:space-y-6">
                    {comments.map((comment: any) => (
                      <div key={comment.id} className="flex gap-3 md:gap-4">
                        <Avatar className="h-8 w-8 md:h-10 md:w-10">
                          <img src={comment.avatar} alt={comment.author} className="object-cover" />
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="font-semibold text-sm md:text-base">{comment.author}</span>
                            <span className="text-xs md:text-sm text-gray-500">
                              {new Date(comment.date).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-gray-700 text-sm md:text-base">{comment.text}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-20 md:top-24 space-y-4 md:space-y-6">
                  {/* Share Section */}
                  <Card>
                    <CardContent className="p-4 md:p-6">
                      <h3 className="font-semibold mb-3 md:mb-4 text-sm md:text-base">Share this article</h3>
                      <div className="space-y-2 md:space-y-3">
                        <Button 
                          variant="outline" 
                          className="w-full justify-start text-xs md:text-sm h-8 md:h-10"
                          onClick={() => window.open(`https://facebook.com/sharer/sharer.php?u=${shareUrl}`, '_blank')}
                        >
                          <Facebook className="h-3 w-3 md:h-4 md:w-4 mr-2" />
                          Facebook
                        </Button>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start text-xs md:text-sm h-8 md:h-10"
                          onClick={() => window.open(`https://twitter.com/intent/tweet?url=${shareUrl}&text=${post.title}`, '_blank')}
                        >
                          <Twitter className="h-3 w-3 md:h-4 md:w-4 mr-2" />
                          Twitter
                        </Button>
                        <Button 
                          variant="outline" 
                          className="w-full justify-start text-xs md:text-sm h-8 md:h-10"
                          onClick={() => window.open(`https://linkedin.com/sharing/share-offsite/?url=${shareUrl}`, '_blank')}
                        >
                          <Linkedin className="h-3 w-3 md:h-4 md:w-4 mr-2" />
                          LinkedIn
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Author Bio */}
                  <Card>
                    <CardContent className="p-4 md:p-6">
                      <h3 className="font-semibold mb-3 md:mb-4 text-sm md:text-base">About the Author</h3>
                      <div className="flex items-center gap-3 mb-3">
                        <Avatar className="h-10 w-10 md:h-12 md:w-12">
                          <img src={post.authorAvatar} alt={post.author} className="object-cover" />
                        </Avatar>
                        <div>
                          <p className="font-semibold text-sm md:text-base">{post.author}</p>
                          <p className="text-xs md:text-sm text-gray-600">Safari Expert</p>
                        </div>
                      </div>
                      <p className="text-xs md:text-sm text-gray-600">
                        Safari guide and wildlife photographer with over 10 years of experience 
                        exploring Tanzania's magnificent national parks.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default BlogPost;
