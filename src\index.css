
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
  }

  /* Ensure full height on mobile devices */
  html, body {
    height: 100%;
    min-height: 100vh;
    /* Fix iOS Safari viewport height issues */
    min-height: -webkit-fill-available;
  }
}

/* Mobile-first responsive utilities */
@layer utilities {
  /* Touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-friendly spacing */
  .mobile-spacing {
    @apply px-4 sm:px-6 md:px-8;
  }

  /* Responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl md:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl md:text-4xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl md:text-5xl;
  }

  /* Mobile navigation fixes */
  .mobile-nav-safe {
    /* Account for mobile browser UI */
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Prevent zoom on input focus (iOS Safari) */
  .no-zoom {
    font-size: 16px;
  }

  /* Mobile-friendly button spacing */
  .mobile-button {
    @apply px-4 py-3 text-base;
  }

  @screen sm {
    .mobile-button {
      @apply px-6 py-2 text-sm;
    }
  }

  /* Responsive grid utilities */
  .grid-responsive-1 {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-responsive-2 {
    @apply grid-cols-1 md:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
  }

  /* Mobile-first card spacing */
  .card-mobile {
    @apply p-4 sm:p-6;
  }

  /* 🦁 SAVANNAH WILDLIFE THEME STYLES */

  /* Savannah-inspired text effects */
  .text-savannah-gradient {
    @apply bg-gradient-to-r from-savannah-600 via-sunset-500 to-savannah-700 bg-clip-text text-transparent;
  }

  .text-wildlife-gold {
    @apply bg-gradient-to-r from-wildlife-lion via-wildlife-leopard to-wildlife-cheetah bg-clip-text text-transparent;
  }

  /* Savannah card styles */
  .card-savannah {
    @apply bg-gradient-to-br from-savannah-50 to-acacia-50 border-savannah-200 shadow-lg hover:shadow-xl transition-all duration-300;
    background-image: var(--tw-gradient-to-r), url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23f5e197" fill-opacity="0.03"%3E%3Cpath d="M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm15 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  }

  .card-wildlife {
    @apply bg-gradient-to-br from-white to-savannah-50 border-acacia-200 shadow-md hover:shadow-2xl transition-all duration-500;
    background-image: url('data:image/svg+xml,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23d4924a" fill-opacity="0.04" fill-rule="evenodd"%3E%3Ccircle cx="3" cy="3" r="3"/%3E%3Ccircle cx="13" cy="13" r="3"/%3E%3C/g%3E%3C/svg%3E');
  }

  /* Savannah button styles */
  .btn-savannah {
    @apply bg-gradient-to-r from-savannah-500 via-sunset-500 to-savannah-600 text-white font-semibold px-6 py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
  }

  .btn-wildlife {
    @apply bg-gradient-to-r from-wildlife-lion to-wildlife-cheetah text-white font-bold px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-110 transition-all duration-300;
  }

  .btn-acacia {
    @apply bg-gradient-to-r from-acacia-600 to-baobab-600 text-white font-medium px-6 py-3 rounded-lg border-2 border-acacia-700 hover:border-acacia-800 shadow-md hover:shadow-lg transition-all duration-300;
  }

  /* Savannah backgrounds */
  .bg-savannah-texture {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(245, 225, 151, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(237, 199, 95, 0.1) 0%, transparent 50%),
      url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23f5e197" fill-opacity="0.05"%3E%3Cpath d="M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm15 0c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  }

  .bg-horizon-gradient {
    background: linear-gradient(180deg,
      #fef7ed 0%,     /* Dawn sky */
      #fdedd3 20%,    /* Morning sky */
      #f6ba6d 40%,    /* Golden hour */
      #f19332 60%,    /* Sunset */
      #ed7014 80%,    /* Deep sunset */
      #b8763e 100%    /* Horizon earth */
    );
  }

  .bg-wildlife-pattern {
    background-image:
      url('data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23c8b588" fill-opacity="0.06" fill-rule="evenodd"%3E%3Cpath d="M0 40L40 0H20L0 20M40 40V20L20 40"/%3E%3C/g%3E%3C/svg%3E'),
      radial-gradient(circle at 30% 70%, rgba(212, 146, 74, 0.1) 0%, transparent 50%);
  }

  /* Savannah hover effects */
  .hover-wildlife {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-xl;
  }

  .hover-wildlife:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(212, 146, 74, 0.2);
  }

  /* Savannah borders and dividers */
  .border-savannah-pattern {
    border-image: linear-gradient(90deg, #f5e197, #edc75f, #d4924a, #b8763e) 1;
  }

  .divider-acacia {
    @apply h-px bg-gradient-to-r from-transparent via-acacia-300 to-transparent;
  }

  /* Safari-themed icons and decorations */
  .icon-wildlife {
    @apply text-wildlife-lion drop-shadow-sm;
  }

  .decoration-savannah {
    @apply relative;
  }

  .decoration-savannah::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #f5e197, #edc75f, #d4924a, #b8763e);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .decoration-savannah:hover::before {
    opacity: 0.3;
  }

  /* Responsive savannah spacing */
  .spacing-savannah {
    @apply space-y-6 md:space-y-8 lg:space-y-12;
  }

  .padding-savannah {
    @apply p-6 md:p-8 lg:p-12;
  }

  .margin-savannah {
    @apply m-4 md:m-6 lg:m-8;
  }
}

/* Animation Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation Classes */
.animate-float {
  animation: float 5s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

/* Staggered Animation Delays */
.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

/* Parallax Background */
.parallax-bg {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Mobile parallax fix */
@media (max-width: 768px) {
  .parallax-bg {
    background-attachment: scroll;
  }
}

/* Seamless Transitions */
.page-transition {
  transition: all 0.3s ease-in-out;
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Mobile hover states - disable on touch devices */
@media (hover: none) and (pointer: coarse) {
  .hover-scale:hover {
    transform: none;
  }
}

/* Improved Focus States */
*:focus-visible {
  outline: 2px solid #f97316;
  outline-offset: 2px;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', sans-serif;
  line-height: 1.2;
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  /* Prevent text selection on touch */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Better touch feedback */
  button, a {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Improve scrolling on iOS */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders */
  .border {
    border-width: 0.5px;
  }
}

/* Loading states for better UX */
.skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.loading-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

/* Accessible focus indicators */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2;
}

/* Mobile-safe sticky positioning */
.sticky-mobile {
  position: -webkit-sticky;
  position: sticky;
}
