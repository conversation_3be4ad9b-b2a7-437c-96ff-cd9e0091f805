
import React from 'react';
import Header from '@/components/layout/Header';
import HeroSection from '@/components/home/<USER>';
import ValuePropositions from '@/components/home/<USER>';
import FeaturedTours from '@/components/home/<USER>';
import DestinationShowcase from '@/components/home/<USER>';
import TestimonialsSection from '@/components/home/<USER>';
import Footer from '@/components/layout/Footer';
import WildlifeTracker from '@/components/features/WildlifeTracker';
import WeatherWidget from '@/components/features/WeatherWidget';
import SustainabilityDashboard from '@/components/features/SustainabilityDashboard';
import VirtualTour from '@/components/features/VirtualTour';

const Index = () => {
  const virtualTourImages = [
    'photo-1472396961693-142e6e269027',
    'photo-1466721591366-2d5fba72006d',
    'photo-1493962853295-0fd70327578a',
    'photo-1485833077593-4278bba3f11f'
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        <HeroSection />
        <ValuePropositions />
        
        {/* New Features Section */}
        <section className="py-12 md:py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-8 md:mb-12">
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-3 md:mb-4">Smart Safari Planning</h2>
              <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                Experience our advanced features designed to make your safari planning intelligent and effortless
              </p>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8 mb-8 md:mb-12">
              <WildlifeTracker />
              <WeatherWidget />
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
              <SustainabilityDashboard />
              <VirtualTour 
                destination="Serengeti National Park" 
                images={virtualTourImages} 
              />
            </div>
          </div>
        </section>
        
        <FeaturedTours />
        <DestinationShowcase />
        <TestimonialsSection />
      </main>
      <Footer />
    </div>
  );
};

export default Index;
