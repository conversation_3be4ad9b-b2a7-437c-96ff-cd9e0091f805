
import React from 'react';
import { Button } from '@/components/ui/button';
import { Heart } from 'lucide-react';
import { useWishlist } from '@/contexts/WishlistContext';
import { useLanguage } from '@/contexts/LanguageContext';

interface WishlistButtonProps {
  item: {
    id: string;
    title: string;
    price: number;
    image: string;
    type: 'tour' | 'destination';
  };
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

const WishlistButton: React.FC<WishlistButtonProps> = ({ item, size = 'default' }) => {
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { t } = useLanguage();
  const inWishlist = isInWishlist(item.id);

  const handleToggle = () => {
    if (inWishlist) {
      removeFromWishlist(item.id);
    } else {
      addToWishlist(item);
    }
  };

  return (
    <Button
      variant={inWishlist ? "default" : "outline"}
      size={size}
      onClick={handleToggle}
      className={`${inWishlist ? 'bg-red-600 hover:bg-red-700' : ''}`}
    >
      <Heart className={`h-4 w-4 mr-2 ${inWishlist ? 'fill-current' : ''}`} />
      {t('wishlist.add')}
    </Button>
  );
};

export default WishlistButton;
