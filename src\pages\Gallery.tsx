
import React, { useState, useEffect, useRef } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Camera, Play, Download, Share2, Heart, Filter, Eye, Calendar, MapPin } from 'lucide-react';

const Gallery = () => {
  const [filter, setFilter] = useState('all');
  const [selectedImage, setSelectedImage] = useState<any>(null);
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set());
  const parallaxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (parallaxRef.current) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.3;
        parallaxRef.current.style.transform = `translateY(${rate}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const galleryItems = [
    {
      id: 1,
      type: 'image',
      category: 'wildlife',
      title: 'Majestic Lion Pride',
      location: 'Serengeti National Park',
      image: 'https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=800&h=1200',
      photographer: 'Sarah Johnson',
      date: '2024-01-15',
      likes: 234,
      views: 1250,
      description: 'A magnificent lion pride resting under the African sun in the heart of Serengeti.',
    },
    {
      id: 2,
      type: 'video',
      category: 'landscapes',
      title: 'Serengeti Sunset',
      location: 'Serengeti National Park',
      image: 'https://images.unsplash.com/photo-1466721591366-2d5fba72006d?auto=format&fit=crop&w=800&h=600',
      photographer: 'David Mwasumbi',
      date: '2024-01-20',
      likes: 189,
      views: 980,
      description: 'Golden hour magic as the sun sets over the endless plains of Serengeti.',
    },
    {
      id: 3,
      type: 'image',
      category: 'wildlife',
      title: 'Elephant Family',
      location: 'Tarangire National Park',
      image: 'https://images.unsplash.com/photo-1493962853295-0fd70327578a?auto=format&fit=crop&w=800&h=800',
      photographer: 'Emily Waters',
      date: '2024-01-25',
      likes: 156,
      views: 845,
      description: 'A gentle elephant family crossing the savanna with ancient baobab trees in the background.',
    },
    {
      id: 4,
      type: 'image',
      category: 'landscapes',
      title: 'Ngorongoro Crater',
      location: 'Ngorongoro Conservation Area',
      image: 'https://images.unsplash.com/photo-1485833077593-4278bba3f11f?auto=format&fit=crop&w=800&h=1000',
      photographer: 'Sarah Johnson',
      date: '2024-02-01',
      likes: 298,
      views: 1567,
      description: 'The breathtaking view of Ngorongoro Crater, often called the "Eighth Wonder of the World".',
    },
    {
      id: 5,
      type: 'video',
      category: 'wildlife',
      title: 'Great Migration',
      location: 'Mara River',
      image: 'https://images.unsplash.com/photo-1500673922987-e212871fec22?auto=format&fit=crop&w=800&h=600',
      photographer: 'David Mwasumbi',
      date: '2024-02-10',
      likes: 445,
      views: 2134,
      description: 'Witness the incredible Great Migration as thousands of wildebeest cross the Mara River.',
    },
    {
      id: 6,
      type: 'image',
      category: 'culture',
      title: 'Maasai Culture',
      location: 'Maasai Village',
      image: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&h=900',
      photographer: 'Emily Waters',
      date: '2024-02-15',
      likes: 167,
      views: 723,
      description: 'Traditional Maasai warriors sharing their rich cultural heritage with safari guests.',
    },
    {
      id: 7,
      type: 'image',
      category: 'wildlife',
      title: 'Leopard in Tree',
      location: 'Serengeti National Park',
      image: 'https://images.unsplash.com/photo-1564760055775-d63b17a55c44?auto=format&fit=crop&w=800&h=1100',
      photographer: 'Michael Chen',
      date: '2024-02-18',
      likes: 312,
      views: 1456,
      description: 'A solitary leopard resting in an acacia tree during the afternoon heat.',
    },
    {
      id: 8,
      type: 'image',
      category: 'landscapes',
      title: 'Kilimanjaro Peak',
      location: 'Mount Kilimanjaro',
      image: 'https://images.unsplash.com/photo-1589182373726-e4f658ab50f2?auto=format&fit=crop&w=800&h=600',
      photographer: 'James Wilson',
      date: '2024-02-20',
      likes: 278,
      views: 1289,
      description: 'The snow-capped peak of Mount Kilimanjaro at sunrise.',
    },
    {
      id: 9,
      type: 'image',
      category: 'wildlife',
      title: 'Cheetah Hunt',
      location: 'Serengeti National Park',
      image: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?auto=format&fit=crop&w=800&h=800',
      photographer: 'Sarah Johnson',
      date: '2024-02-22',
      likes: 423,
      views: 1987,
      description: 'A cheetah in full sprint chasing its prey across the savanna.',
    },
    {
      id: 10,
      type: 'image',
      category: 'culture',
      title: 'Traditional Dance',
      location: 'Maasai Village',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?auto=format&fit=crop&w=800&h=1000',
      photographer: 'Grace Mollel',
      date: '2024-02-25',
      likes: 189,
      views: 834,
      description: 'Maasai warriors performing their traditional jumping dance.',
    },
    {
      id: 11,
      type: 'image',
      category: 'wildlife',
      title: 'Zebra Crossing',
      location: 'Ngorongoro Crater',
      image: 'https://images.unsplash.com/photo-1551969014-7d2c4cddf0b6?auto=format&fit=crop&w=800&h=700',
      photographer: 'David Mwasumbi',
      date: '2024-02-28',
      likes: 267,
      views: 1123,
      description: 'A dazzle of zebras crossing the crater floor in perfect formation.',
    },
    {
      id: 12,
      type: 'image',
      category: 'landscapes',
      title: 'Baobab Sunset',
      location: 'Tarangire National Park',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?auto=format&fit=crop&w=800&h=1200',
      photographer: 'Michael Chen',
      date: '2024-03-01',
      likes: 345,
      views: 1567,
      description: 'Ancient baobab trees silhouetted against a vibrant African sunset.',
    }
  ];

  const categories = ['all', 'wildlife', 'landscapes', 'culture'];

  const filteredItems = filter === 'all' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === filter);

  const handleImageLoad = (id: number) => {
    setLoadedImages(prev => new Set([...prev, id]));
  };

  // Stable Masonry Grid Component
  const MasonryGrid = ({ items }: { items: typeof galleryItems }) => {
    return (
      <div className="masonry-grid" style={{
        columnCount: 'auto',
        columnWidth: '300px',
        columnGap: '1.5rem',
        width: '100%'
      }}>
        {items.map((item) => (
          <div 
            key={item.id} 
            className="masonry-item mb-6"
            style={{ 
              breakInside: 'avoid',
              display: 'inline-block',
              width: '100%'
            }}
          >
            <Card 
              className="group overflow-hidden hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 cursor-pointer"
              onClick={() => setSelectedImage(item)}
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden">
                  {/* Image with consistent aspect ratio */}
                  <div className="relative bg-gray-200">
                    <img
                      src={item.image}
                      alt={item.title}
                      className={`w-full h-auto object-cover group-hover:scale-110 transition-transform duration-700 ${
                        loadedImages.has(item.id) ? 'opacity-100' : 'opacity-0'
                      }`}
                      onLoad={() => handleImageLoad(item.id)}
                      style={{ 
                        display: 'block',
                        maxWidth: '100%',
                        height: 'auto'
                      }}
                    />
                    
                    {/* Loading placeholder with fixed height */}
                    {!loadedImages.has(item.id) && (
                      <div 
                        className="absolute inset-0 bg-gray-300 animate-pulse flex items-center justify-center"
                        style={{ minHeight: '200px' }}
                      >
                        <Camera className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Video Indicator */}
                  {item.type === 'video' && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-white/90 rounded-full p-4 group-hover:scale-110 transition-transform">
                        <Play className="h-8 w-8 text-orange-600" />
                      </div>
                    </div>
                  )}

                  {/* Category Badge */}
                  <Badge className="absolute top-3 left-3 bg-orange-600 text-white shadow-lg capitalize">
                    {item.category}
                  </Badge>

                  {/* Stats Overlay */}
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="bg-black/70 rounded-lg px-3 py-1 text-white text-sm flex items-center gap-2">
                      <Eye className="h-3 w-3" />
                      {item.views}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                    <Button size="sm" variant="ghost" className="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" className="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full">
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost" className="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Overlay Info */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent p-4 text-white transform translate-y-2 group-hover:translate-y-0 transition-transform">
                    <h3 className="font-semibold text-lg mb-1 line-clamp-2">{item.title}</h3>
                    <div className="flex items-center gap-2 text-sm opacity-90 mb-2">
                      <MapPin className="h-3 w-3" />
                      <span className="line-clamp-1">{item.location}</span>
                    </div>
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <Camera className="h-3 w-3" />
                        <span>{item.photographer}</span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1">
                          <Heart className="h-3 w-3" />
                          <span>{item.likes}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(item.date).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section with Parallax */}
        <div className="relative h-96 overflow-hidden">
          <div 
            ref={parallaxRef}
            className="absolute inset-0 w-full h-120"
            style={{
              backgroundImage: 'url(https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=1920&h=1080)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-4xl mx-auto">
              <Badge className="mb-4 bg-white/20 text-white px-4 py-2">
                <Camera className="w-4 h-4 mr-2" />
                Photo Gallery
              </Badge>
              <h1 className="text-5xl md:text-6xl font-bold mb-6">Safari Memories</h1>
              <p className="text-xl md:text-2xl leading-relaxed opacity-90">
                Explore our collection of breathtaking wildlife photography and unforgettable safari moments captured across Tanzania's most spectacular destinations.
              </p>
            </div>
          </div>
        </div>

        {/* Gallery Controls */}
        <div className="bg-white border-b border-gray-200 sticky top-20 z-40">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              {/* Category Filters */}
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5 text-gray-500 mr-2" />
                <span className="text-sm font-medium text-gray-700 mr-4">Filter by:</span>
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={filter === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilter(category)}
                    className={filter === category ? "bg-orange-600 hover:bg-orange-700" : "hover:bg-orange-50"}
                  >
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </Button>
                ))}
              </div>

              {/* Stats */}
              <div className="text-sm text-gray-600">
                Showing {filteredItems.length} of {galleryItems.length} photos
              </div>
            </div>
          </div>
        </div>

        {/* Masonry Gallery Grid */}
        <div className="container mx-auto px-4 py-12">
          <MasonryGrid items={filteredItems} />
        </div>

        {/* Load More Button */}
        <div className="text-center pb-16">
          <Button className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3">
            Load More Photos
          </Button>
        </div>

        {/* Modal for Selected Image */}
        {selectedImage && (
          <div 
            className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedImage(null)}
          >
            <div className="max-w-6xl w-full bg-white rounded-lg overflow-hidden" onClick={(e) => e.stopPropagation()}>
              <div className="relative">
                <img
                  src={selectedImage.image}
                  alt={selectedImage.title}
                  className="w-full max-h-96 object-cover"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-4 right-4 bg-white/90 hover:bg-white text-gray-800"
                  onClick={() => setSelectedImage(null)}
                >
                  ✕
                </Button>
                {selectedImage.type === 'video' && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Button className="bg-orange-600 hover:bg-orange-700 text-white rounded-full p-6">
                      <Play className="h-8 w-8" />
                    </Button>
                  </div>
                )}
              </div>
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">{selectedImage.title}</h2>
                    <div className="flex items-center gap-4 text-gray-600 mb-2">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        {selectedImage.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <Camera className="h-4 w-4" />
                        {selectedImage.photographer}
                      </div>
                    </div>
                    <Badge variant="outline" className="text-orange-600 border-orange-600 capitalize">
                      {selectedImage.category}
                    </Badge>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <Heart className="h-4 w-4 mr-2" />
                      {selectedImage.likes}
                    </Button>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      {selectedImage.views}
                    </Button>
                    <Button size="sm" variant="outline">
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <p className="text-gray-600 mb-4">{selectedImage.description}</p>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Captured on {new Date(selectedImage.date).toLocaleDateString()}</span>
                  <span>Type: {selectedImage.type}</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
      <Footer />
    </div>
  );
};

export default Gallery;
