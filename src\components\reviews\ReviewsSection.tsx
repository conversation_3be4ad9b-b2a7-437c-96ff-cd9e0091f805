
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Star, Filter } from 'lucide-react';
import ReviewCard from './ReviewCard';
import ReviewForm from './ReviewForm';

interface ReviewsSectionProps {
  tourId: string;
  tourName: string;
}

const ReviewsSection: React.FC<ReviewsSectionProps> = ({ tourId, tourName }) => {
  const [reviews, setReviews] = useState<any[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [filterRating, setFilterRating] = useState(0);

  useEffect(() => {
    // Mock reviews data
    const mockReviews = [
      {
        id: '1',
        author: '<PERSON>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?auto=format&fit=crop&w=150&h=150',
        rating: 5,
        title: 'Absolutely incredible experience!',
        content: 'The guides were knowledgeable, the accommodations were luxurious, and seeing the Great Migration was a dream come true. Every detail was perfectly planned.',
        date: '2 weeks ago',
        tourName,
        verified: true,
        helpful: 12,
        photos: ['photo-1472396961693-142e6e269027', 'photo-1466721591366-2d5fba72006d']
      },
      {
        id: '2',
        author: 'Michael Chen',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&h=150',
        rating: 5,
        title: 'Exceeded all expectations',
        content: 'The attention to detail was phenomenal. Every day brought new adventures and the wildlife viewing exceeded all expectations. Highly recommend!',
        date: '1 month ago',
        tourName,
        verified: true,
        helpful: 8
      },
      {
        id: '3',
        author: 'Emma Rodriguez',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=150&h=150',
        rating: 4,
        title: 'Great safari with minor issues',
        content: 'Overall fantastic experience. The wildlife sightings were amazing and our guide was very knowledgeable. Only minor complaint was some of the accommodations could have been better.',
        date: '3 weeks ago',
        tourName,
        verified: true,
        helpful: 5
      }
    ];
    setReviews(mockReviews);
  }, [tourName]);

  const handleReviewSubmit = (newReview: any) => {
    setReviews([newReview, ...reviews]);
    setShowForm(false);
  };

  const handleHelpful = (reviewId: string) => {
    setReviews(reviews.map(review => 
      review.id === reviewId 
        ? { ...review, helpful: review.helpful + 1 }
        : review
    ));
  };

  const handleReport = (reviewId: string) => {
    console.log('Reported review:', reviewId);
    // In real app, handle reporting logic
  };

  const filteredReviews = filterRating 
    ? reviews.filter(review => review.rating === filterRating)
    : reviews;

  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0;

  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(review => review.rating === rating).length,
    percentage: reviews.length > 0 
      ? (reviews.filter(review => review.rating === rating).length / reviews.length) * 100 
      : 0
  }));

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Reviews</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Overall Rating */}
            <div className="text-center">
              <div className="text-4xl font-bold text-orange-600 mb-2">
                {averageRating.toFixed(1)}
              </div>
              <div className="flex justify-center mb-2">
                {[...Array(5)].map((_, i) => (
                  <Star 
                    key={i} 
                    className={`h-5 w-5 ${i < Math.floor(averageRating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                  />
                ))}
              </div>
              <p className="text-gray-600">Based on {reviews.length} reviews</p>
            </div>

            {/* Rating Distribution */}
            <div>
              {ratingDistribution.map(({ rating, count, percentage }) => (
                <div key={rating} className="flex items-center space-x-2 mb-2">
                  <span className="text-sm w-8">{rating}★</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-orange-600 h-2 rounded-full" 
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 w-8">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Review Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4" />
          <span className="text-sm">Filter by rating:</span>
          <div className="flex space-x-1">
            <Button
              variant={filterRating === 0 ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterRating(0)}
            >
              All
            </Button>
            {[5, 4, 3, 2, 1].map(rating => (
              <Button
                key={rating}
                variant={filterRating === rating ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterRating(rating)}
              >
                {rating}★
              </Button>
            ))}
          </div>
        </div>
        <Button onClick={() => setShowForm(!showForm)}>
          {showForm ? 'Cancel' : 'Write Review'}
        </Button>
      </div>

      {/* Review Form */}
      {showForm && (
        <ReviewForm
          tourId={tourId}
          tourName={tourName}
          onSubmit={handleReviewSubmit}
        />
      )}

      {/* Reviews List */}
      <div>
        {filteredReviews.length > 0 ? (
          filteredReviews.map(review => (
            <ReviewCard
              key={review.id}
              review={review}
              onHelpful={handleHelpful}
              onReport={handleReport}
            />
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">No reviews found for the selected rating.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ReviewsSection;
