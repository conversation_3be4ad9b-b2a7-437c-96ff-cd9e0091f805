
import { FirebaseService } from './firebase';
import { Booking, Tour, UserProfile } from '@/types/firebase';
import { Timestamp } from 'firebase/firestore';

export class BookingService {
  // Create a new booking
  static async createBooking(bookingData: any, userId: string, userEmail: string) {
    try {
      console.log('Creating booking:', bookingData);
      
      // Get tour details if it's not a custom tour
      let tourTitle = 'Custom Safari Tour';
      let tourPrice = bookingData.totalPrice;
      
      if (!bookingData.tourId?.startsWith('custom-')) {
        const tour = await FirebaseService.getTour(bookingData.tourId);
        if (tour) {
          tourTitle = tour.title;
          tourPrice = tour.price;
        }
      }

      // Calculate pricing
      const accommodationPrices = {
        budget: 0,
        midrange: 150,
        luxury: 400,
      };
      
      const addOnPrices = {
        photography: 75,
        cultural: 50,
        balloon: 550,
        'night-drive': 100,
      };

      const basePrice = tourPrice;
      const accommodationPrice = accommodationPrices[bookingData.accommodation as keyof typeof accommodationPrices] || 0;
      const addOnsPrice = bookingData.addOns.reduce((total: number, addon: string) => 
        total + (addOnPrices[addon as keyof typeof addOnPrices] || 0), 0
      );
      
      const totalPrice = (basePrice + accommodationPrice + addOnsPrice) * bookingData.groupSize;
      const depositAmount = Math.round(totalPrice * 0.3); // 30% deposit

      const booking: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'> = {
        userId,
        tourId: bookingData.tourId,
        tourTitle,
        userName: bookingData.travelers[0]?.firstName + ' ' + bookingData.travelers[0]?.lastName || 'Guest',
        userEmail,
        startDate: bookingData.startDate?.toISOString().split('T')[0] || '',
        endDate: this.calculateEndDate(bookingData.startDate, tourTitle),
        guests: bookingData.groupSize,
        travelers: bookingData.travelers.map((traveler: any) => ({
          ...traveler,
          dateOfBirth: traveler.dateOfBirth || new Date(),
          dietaryRequirements: [],
          medicalConditions: [],
          fitnessLevel: 'moderate',
          photographyExperience: 'beginner',
          birdingExperience: 'beginner'
        })),
        accommodation: bookingData.accommodation,
        addOns: bookingData.addOns,
        specialRequests: bookingData.specialRequests,
        totalPrice,
        depositPaid: 0,
        balanceRemaining: totalPrice,
        status: 'pending',
        paymentStatus: 'pending',
        groupOption: 'shared',
        dietaryRequirements: [],
        medicalConditions: [],
        fitnessLevel: 'moderate'
      };

      const result = await FirebaseService.createBooking(booking);
      
      // Create initial payment transaction
      await this.createPaymentTransaction(result.id, totalPrice, depositAmount, 'deposit');
      
      // Send confirmation notification
      await this.sendBookingConfirmationNotification(userId, result.id, tourTitle);
      
      return result;
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  }

  // Calculate end date based on tour duration
  private static calculateEndDate(startDate: Date | null, tourTitle: string): string {
    if (!startDate) return '';
    
    // Extract days from tour title or default to 7 days
    const daysMatch = tourTitle.match(/(\d+)\s*days?/i);
    const duration = daysMatch ? parseInt(daysMatch[1]) : 7;
    
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + duration - 1);
    
    return endDate.toISOString().split('T')[0];
  }

  // Create payment transaction
  private static async createPaymentTransaction(bookingId: string, totalAmount: number, amount: number, type: 'deposit' | 'balance') {
    try {
      const transaction = {
        bookingId,
        userId: '', // Will be set by the booking system
        amount,
        currency: 'USD',
        type,
        status: 'pending' as const,
        paymentMethod: 'credit_card',
        transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        description: `${type === 'deposit' ? 'Deposit' : 'Balance'} payment for booking ${bookingId}`
      };

      return await FirebaseService.createPaymentTransaction(transaction);
    } catch (error) {
      console.error('Error creating payment transaction:', error);
      throw error;
    }
  }

  // Send booking confirmation notification
  private static async sendBookingConfirmationNotification(userId: string, bookingId: string, tourTitle: string) {
    try {
      const notification = {
        userId,
        title: 'Booking Confirmation',
        message: `Your booking for "${tourTitle}" has been received and is being processed.`,
        type: 'booking' as const,
        read: false,
        priority: 'medium' as const,
        actionUrl: `/dashboard`,
        data: { bookingId },
        createdAt: Timestamp.now()
      };

      return await FirebaseService.createNotification(notification);
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  // Update booking status
  static async updateBookingStatus(bookingId: string, status: Booking['status']) {
    try {
      return await FirebaseService.updateBooking(bookingId, { status });
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw error;
    }
  }

  // Process payment
  static async processPayment(bookingId: string, amount: number, paymentMethod: string) {
    try {
      // Simulate payment processing
      const isSuccessful = Math.random() > 0.1; // 90% success rate for demo
      
      if (isSuccessful) {
        // Update booking payment status
        await FirebaseService.updateBooking(bookingId, {
          paymentStatus: 'paid',
          status: 'confirmed'
        });

        // Create successful payment transaction
        await this.createPaymentTransaction(bookingId, amount, amount, 'balance');
        
        return { success: true, transactionId: `txn_${Date.now()}` };
      } else {
        throw new Error('Payment failed');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  }

  // Get user bookings
  static async getUserBookings(userId: string) {
    try {
      return await FirebaseService.getBookings(userId);
    } catch (error) {
      console.error('Error getting user bookings:', error);
      throw error;
    }
  }

  // Cancel booking
  static async cancelBooking(bookingId: string, reason: string) {
    try {
      await FirebaseService.updateBooking(bookingId, {
        status: 'cancelled',
        specialRequests: reason
      });

      // Create refund transaction
      const booking = await FirebaseService.getBooking(bookingId);
      if (booking && booking.depositPaid > 0) {
        await this.createPaymentTransaction(bookingId, booking.depositPaid, booking.depositPaid, 'balance');
      }

      return true;
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw error;
    }
  }
}
